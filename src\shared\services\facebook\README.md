# Facebook Service

Service này cung cấp các phương thức để tương tác với Facebook API sử dụng Facebook SDK thay vì gọi API trực tiếp.

## Cài đặt

Đảm bảo bạn đã cài đặt package `facebook-nodejs-business-sdk`:

```bash
npm install facebook-nodejs-business-sdk
```

## Cấu hình

Service này yêu cầu các biến môi trường sau:

- `FACEBOOK_APP_ID`: ID của ứng dụng Facebook
- `FACEBOOK_APP_SECRET`: Secret của ứng dụng Facebook
- `FACEBOOK_REDIRECT_URI`: URI callback của ứng dụng Facebook

## Sử dụng

### Import module

```typescript
import { FacebookModule } from '@shared/services/facebook/facebook.module';

@Module({
  imports: [
    FacebookModule,
    // ...
  ],
})
export class YourModule {}
```

### Sử dụng service

```typescript
import { FacebookService } from '@shared/services/facebook/facebook.service';

@Injectable()
export class YourService {
  constructor(private readonly facebookService: FacebookService) {}

  // Sử dụng các phương thức của FacebookService
}
```

## Các phương thức

### Tạo URL xác thực

```typescript
// Tạo URL xác thực Facebook với redirectUri linh hoạt
const redirectUri = 'https://your-app.com/auth/facebook/callback';
const authUrl = facebookService.createAuthUrl(redirectUri, 'user_id_123', [
  'pages_show_list',
  'pages_messaging',
]);
```

### Xử lý callback

```typescript
// Xử lý callback từ Facebook
const redirectUri = 'https://your-app.com/auth/facebook/callback'; // Phải giống với URI đã dùng khi tạo auth URL
const authResponse = await facebookService.handleCallback(code, redirectUri);
const { access_token, expires_in } = authResponse;
```

### Lấy thông tin người dùng

```typescript
// Lấy thông tin người dùng Facebook
const userInfo = await facebookService.getUserInfo(accessToken);
```

### Lấy danh sách trang

```typescript
// Lấy danh sách trang Facebook mà người dùng quản lý
const pages = await facebookService.getUserPages(accessToken);

// Lấy danh sách trang với các trường tùy chỉnh
const pagesWithCustomFields = await facebookService.getUserPages(
  accessToken,
  'id,name,access_token,category,picture,fan_count,verification_status'
);

// Lấy danh sách trang Facebook được gán cho người dùng (API v22.0+)
const assignedPages = await facebookService.getUserAssignedPages(accessToken);
```

### Lấy thông tin chi tiết trang

```typescript
// Lấy thông tin chi tiết của trang Facebook
const pageDetails = await facebookService.getPageDetails(pageId, pageAccessToken);

// Lấy thông tin chi tiết với các trường tùy chỉnh
const pageDetailsWithCustomFields = await facebookService.getPageDetails(
  pageId,
  pageAccessToken,
  'id,name,access_token,category,picture,fan_count,verification_status,about,description'
);
```

### Gửi tin nhắn

```typescript
// Gửi tin nhắn đến người dùng thông qua trang Facebook
const result = await facebookService.sendMessage(
  pageId,
  pageAccessToken,
  userId,
  'Xin chào, tôi là trợ lý ảo của bạn!'
);
```

### Lấy long-lived access token

```typescript
// Chuyển đổi short-lived access token thành long-lived access token (có thời hạn 60 ngày)
const longLivedTokenResponse = await facebookService.getLongLivedToken(accessToken);
const longLivedToken = longLivedTokenResponse.access_token;
const expiresIn = longLivedTokenResponse.expires_in;
```

### Kiểm tra thông tin token

```typescript
// Kiểm tra thông tin và tính hợp lệ của access token
const tokenInfo = await facebookService.getTokenInfo(accessToken);
const isValid = tokenInfo.is_valid;
const expiresAt = tokenInfo.data_access_expires_at;
const userId = tokenInfo.user_id;
```

## Tích hợp với FacebookPageUserService

Để sử dụng FacebookService trong FacebookPageUserService, bạn cần:

1. Import FacebookModule vào module chứa FacebookPageUserService
2. Inject FacebookService vào FacebookPageUserService
3. Sử dụng các phương thức của FacebookService thay vì gọi API trực tiếp

Ví dụ:

```typescript
import { FacebookService } from '@shared/services/facebook/facebook.service';

@Injectable()
export class FacebookPageUserService {
  constructor(
    // Các dependency hiện tại
    private readonly facebookService: FacebookService,
  ) {
    // ...
  }

  async createFacebookAuthUrl(userId: number): Promise<FacebookAuthResponseDto> {
    try {
      // Sử dụng redirectUri linh hoạt
      const redirectUri = `${process.env.APP_URL}/user/facebook/callback`;
      const authUrl = this.facebookService.createAuthUrl(redirectUri, userId.toString());
      return { authUrl };
    } catch (error) {
      this.logger.error(`Error creating Facebook auth URL: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi tạo URL xác thực Facebook'
      );
    }
  }

  async handleFacebookCallback(userId: number, callbackDto: FacebookCallbackDto): Promise<FacebookPersonalResponseDto> {
    try {
      const { code } = callbackDto;

      // Sử dụng redirectUri giống với URI đã dùng khi tạo auth URL
      const redirectUri = `${process.env.APP_URL}/user/facebook/callback`;

      // Sử dụng FacebookService để lấy access token
      const authResponse = await this.facebookService.handleCallback(code, redirectUri);
      const { access_token, expires_in } = authResponse;

      // Chuyển đổi thành long-lived access token (có thời hạn 60 ngày)
      const longLivedTokenResponse = await this.facebookService.getLongLivedToken(access_token);
      const longLivedToken = longLivedTokenResponse.access_token;
      const longLivedExpiresIn = longLivedTokenResponse.expires_in;

      // Lấy thông tin người dùng Facebook
      const userInfo = await this.facebookService.getUserInfo(longLivedToken);
      const { id: facebookPersonalId } = userInfo;

      // Tính thời gian hết hạn
      const expirationDate = new Date();
      expirationDate.setSeconds(expirationDate.getSeconds() + longLivedExpiresIn);
      const expirationDateUnix = Math.floor(expirationDate.getTime());

      // Tiếp tục xử lý như hiện tại...
    } catch (error) {
      // Xử lý lỗi...
    }
  }
}
```
