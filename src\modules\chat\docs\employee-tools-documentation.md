# Employee Tools Documentation

## Overview
Employee Tools Provider cung cấp một bộ công cụ toàn diện để quản lý nhân viên thông qua chat AI. Các tools được chia thành 6 nhóm chính:

1. **Employee Management Tools** - <PERSON><PERSON><PERSON><PERSON> lý thông tin nhân viên cơ bản
2. **User Management Tools** - Quản lý tài khoản người dùng
3. **Employee Statistics Tools** - Thống kê và báo cáo nhân viên
4. **Employee-User Management Tools** - Quản lý liên kết nhân viên-user
5. **Permission Management Tools** - Quản lý phân quyền
6. **Role Management Tools** - Quản lý vai trò

## Employee Management Tools

### 1. get_employee_info
**<PERSON><PERSON> tả**: L<PERSON><PERSON> thông tin chi tiết nhân viên
**Tham số**:
- `employeeId` (optional): ID nhân viên
- `employeeName` (optional): <PERSON><PERSON><PERSON> nhân viên (tìm kiếm gần đúng)
- `includeStats` (optional): Bao gồm thống kê hiệu suất

### 2. get_employee_list
**Mô tả**: Lấy danh sách nhân viên theo bộ lọc
**Tham số**:
- `departmentId` (optional): ID phòng ban
- `position` (optional): Chức vụ
- `status` (optional): Trạng thái nhân viên (active/inactive/on_leave)
- `limit` (optional): Số lượng kết quả tối đa (mặc định 20)

### 3. get_late_employees
**Mô tả**: Lấy danh sách nhân viên đi muộn
**Tham số**:
- `date` (optional): Ngày kiểm tra (YYYY-MM-DD), mặc định hôm nay
- `departmentId` (optional): ID phòng ban
- `limit` (optional): Số lượng kết quả tối đa (mặc định 10)

### 4. create_employee
**Mô tả**: Tạo nhân viên mới
**Tham số**:
- `fullName`: Họ và tên đầy đủ
- `email`: Email nhân viên
- `phoneNumber` (optional): Số điện thoại
- `departmentId`: ID phòng ban
- `position` (optional): Chức vụ
- `startDate` (optional): Ngày bắt đầu làm việc (YYYY-MM-DD)

### 5. update_employee
**Mô tả**: Cập nhật thông tin nhân viên
**Tham số**:
- `employeeId`: ID nhân viên
- `fullName` (optional): Họ và tên đầy đủ
- `email` (optional): Email nhân viên
- `phoneNumber` (optional): Số điện thoại
- `departmentId` (optional): ID phòng ban
- `position` (optional): Chức vụ
- `status` (optional): Trạng thái nhân viên

## User Management Tools

### 6. get_user_list
**Mô tả**: Lấy danh sách người dùng với phân trang và bộ lọc
**Tham số**:
- `page` (optional): Số trang (mặc định 1)
- `limit` (optional): Số lượng kết quả trên mỗi trang (mặc định 10)
- `search` (optional): Tìm kiếm theo tên, email, vị trí
- `status` (optional): Trạng thái người dùng (ACTIVE/INACTIVE)
- `departmentId` (optional): ID phòng ban
- `hasEmployee` (optional): Lọc người dùng có liên kết với nhân viên
- `userType` (optional): Loại người dùng

### 7. get_user_info
**Mô tả**: Lấy thông tin chi tiết người dùng theo ID
**Tham số**:
- `userId`: ID người dùng

### 8. get_employees_with_user_accounts
**Mô tả**: Lấy danh sách nhân viên đã có tài khoản user
**Tham số**:
- `page` (optional): Số trang (mặc định 1)
- `limit` (optional): Số lượng kết quả trên mỗi trang (mặc định 10)
- `search` (optional): Tìm kiếm theo tên, email
- `status` (optional): Trạng thái tài khoản (ACTIVE/INACTIVE)
- `departmentId` (optional): ID phòng ban

## Employee Statistics Tools

### 9. get_employee_overview
**Mô tả**: Lấy thống kê tổng quan về nhân viên
**Tham số**: Không có

### 10. get_employee_department_distribution
**Mô tả**: Lấy thống kê phân bố nhân viên theo phòng ban
**Tham số**: Không có

### 11. get_employee_contract_distribution
**Mô tả**: Lấy thống kê phân bố nhân viên theo loại hợp đồng
**Tham số**: Không có

### 12. get_employee_tenure_stats
**Mô tả**: Lấy thống kê thâm niên nhân viên
**Tham số**: Không có

## Employee-User Management Tools

### 13. create_employee_with_user
**Mô tả**: Tạo nhân viên mới kèm tài khoản user
**Tham số**:
- `employeeName`: Tên nhân viên
- `email`: Email cho tài khoản user
- `password`: Mật khẩu cho tài khoản user
- `username` (optional): Tên đăng nhập (mặc định từ email)
- `fullName` (optional): Họ tên đầy đủ (mặc định dùng employeeName)
- `jobTitle` (optional): Chức danh công việc
- `departmentId`: ID phòng ban

### 14. create_user_for_employee
**Mô tả**: Tạo tài khoản user cho nhân viên hiện có
**Tham số**:
- `employeeId`: ID nhân viên
- `email`: Email cho tài khoản user
- `autoGeneratePassword` (optional): Tự động tạo mật khẩu (mặc định false)
- `password` (optional): Mật khẩu (bắt buộc nếu không tự động tạo)

### 15. link_employee_to_user
**Mô tả**: Liên kết nhân viên hiện có với tài khoản user hiện có
**Tham số**:
- `employeeId`: ID nhân viên
- `targetUserId`: ID tài khoản user cần liên kết

## Permission Management Tools

### 16. get_permission_groups
**Mô tả**: Lấy danh sách tất cả quyền được nhóm theo module
**Tham số**: Không có

### 17. get_user_permissions
**Mô tả**: Lấy danh sách quyền của một người dùng
**Tham số**:
- `userId`: ID người dùng

### 18. update_employee_permissions
**Mô tả**: Cập nhật danh sách quyền cho nhân viên (thay thế tất cả quyền hiện tại)
**Tham số**:
- `employeeId`: ID nhân viên
- `permissionIds`: Danh sách ID quyền mới

### 19. update_employee_roles
**Mô tả**: Cập nhật danh sách vai trò cho nhân viên (thay thế tất cả vai trò hiện tại)
**Tham số**:
- `employeeId`: ID nhân viên
- `roleIds`: Danh sách ID vai trò mới

## Role Management Tools

### 20. get_role_list
**Mô tả**: Lấy danh sách vai trò với phân trang và tìm kiếm
**Tham số**:
- `page` (optional): Số trang (mặc định 1)
- `limit` (optional): Số lượng kết quả trên mỗi trang (mặc định 10)
- `search` (optional): Tìm kiếm theo tên vai trò
- `sortBy` (optional): Trường sắp xếp (mặc định 'name')
- `sortDirection` (optional): Hướng sắp xếp (ASC/DESC, mặc định ASC)

### 21. get_role_detail
**Mô tả**: Lấy thông tin chi tiết vai trò bao gồm danh sách quyền
**Tham số**:
- `roleId`: ID vai trò

### 22. update_role_permissions
**Mô tả**: Cập nhật danh sách quyền cho vai trò (thay thế tất cả quyền hiện tại)
**Tham số**:
- `roleId`: ID vai trò
- `permissionIds`: Danh sách ID quyền mới

## Usage Examples

### Lấy thông tin nhân viên
```
"Cho tôi xem thông tin nhân viên Nguyễn Văn A"
```

### Lấy danh sách nhân viên theo phòng ban
```
"Hiển thị danh sách nhân viên phòng IT"
```

### Tạo nhân viên mới
```
"Tạo nhân viên mới tên Trần Thị B, email <EMAIL>, phòng ban ID 2"
```

### Lấy thống kê nhân viên
```
"Cho tôi xem thống kê tổng quan nhân viên"
```

### Gán quyền cho nhân viên
```
"Gán quyền ID 1,2,3 cho nhân viên ID 5"
```

## Notes
- Tất cả tools đều hỗ trợ tenant isolation
- Các thao tác thay đổi dữ liệu yêu cầu quyền phù hợp
- Kết quả trả về dưới dạng JSON được format
- Lỗi được xử lý và trả về thông báo rõ ràng
