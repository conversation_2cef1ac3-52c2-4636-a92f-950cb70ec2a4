import { Injectable } from '@nestjs/common';
import { EmployeeService } from '@modules/hrm/employees/services/employee.service';
// import { UserService } from '@modules/hrm/employees/services/user.service';
// import { EmployeeUserService } from '@modules/hrm/employees/services/employee-user.service';
import { EmployeePermissionService } from '@modules/hrm/employees/services/employee-permission.service';
import { RoleService } from '@modules/hrm/employees/services/role.service';
import { tool, ToolRunnableConfig } from '@langchain/core/tools';
import { z } from 'zod';
import { EmployeeStatus } from '@modules/hrm/employees/enum/employee-status.enum';
// import { UserStatus } from '@modules/auth/enum/user-status.enum';
import { SortDirection } from '@/common/dto/query.dto';

/**
 * Employee Tools Provider
 * Cung cấp các tools liên quan đến quản lý nhân viên
 */
@Injectable()
export class EmployeeToolsProvider {
  constructor(
    private readonly employeeService: EmployeeService,
    // private readonly userService: UserService,
    // private readonly employeeUserService: EmployeeUserService,
    private readonly employeePermissionService: EmployeePermissionService,
    private readonly roleService: RoleService,
  ) {}

  /**
   * Lấy tất cả employee tools
   */
  getTools() {
    return [
      // Lấy thông tin nhân viên
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');
            const userId = parseInt(config?.configurable?.['userId'] || '0');

            let employee;

            if (_args.employeeId) {
              employee = await this.employeeService.findById(tenantId, _args.employeeId);
            } else if (_args.employeeName) {
              const employees = await this.employeeService.searchByName(tenantId, _args.employeeName);
              employee = employees[0];
            } else {
              employee = await this.employeeService.findByUserId(tenantId, userId);
            }

            if (!employee) {
              return 'Không tìm thấy nhân viên';
            }

            return `Thông tin nhân viên ${employee.employeeName || 'N/A'}:\n${JSON.stringify(employee, null, 2)}`;
          } catch (error) {
            return `Lấy thông tin nhân viên thất bại: ${error.message}`;
          }
        },
        {
          name: 'get_employee_info',
          description: 'Lấy thông tin chi tiết nhân viên',
          schema: z.object({
            employeeId: z.number().optional().describe('ID nhân viên'),
            employeeName: z.string().optional().describe('Tên nhân viên (tìm kiếm gần đúng)'),
            includeStats: z.boolean().optional().default(false).describe('Bao gồm thống kê hiệu suất'),
          }),
        }
      ),

      // Lấy danh sách nhân viên
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');

            const employees = await this.employeeService.getEmployeeList(tenantId, {
              departmentId: _args.departmentId,
              position: _args.position,
              status: _args.status,
              limit: _args.limit,
            });

            return `Tìm thấy ${employees.length} nhân viên:\n${JSON.stringify(employees, null, 2)}`;
          } catch (error) {
            return `Lấy danh sách nhân viên thất bại: ${error.message}`;
          }
        },
        {
          name: 'get_employee_list',
          description: 'Lấy danh sách nhân viên theo bộ lọc',
          schema: z.object({
            departmentId: z.number().optional().describe('ID phòng ban'),
            position: z.string().optional().describe('Chức vụ'),
            status: z.enum(['active', 'inactive', 'on_leave']).optional().default('active').describe('Trạng thái nhân viên'),
            limit: z.number().optional().default(20).describe('Số lượng kết quả tối đa'),
          }),
        }
      ),

      // Lấy nhân viên đi muộn
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');
            const date = _args.date || new Date().toISOString().split('T')[0];

            const lateEmployees = await this.employeeService.getLateEmployees(tenantId, new Date(date).getTime(), {
              departmentId: _args.departmentId,
              limit: _args.limit,
            });

            return `Tìm thấy ${lateEmployees.length} nhân viên đi muộn ngày ${date}:\n${JSON.stringify(lateEmployees, null, 2)}`;
          } catch (error) {
            return `Lấy danh sách nhân viên đi muộn thất bại: ${error.message}`;
          }
        },
        {
          name: 'get_late_employees',
          description: 'Lấy danh sách nhân viên đi muộn',
          schema: z.object({
            date: z.string().optional().describe('Ngày kiểm tra (YYYY-MM-DD), mặc định là hôm nay'),
            departmentId: z.number().optional().describe('ID phòng ban'),
            limit: z.number().optional().default(10).describe('Số lượng kết quả tối đa'),
          }),
        }
      ),

      // Tạo nhân viên mới
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');
            const userId = parseInt(config?.configurable?.['userId'] || '0');

            const createEmployeeDto = {
              employeeName: _args.fullName,
              email: _args.email,
              phoneNumber: _args.phoneNumber,
              departmentId: _args.departmentId,
              position: _args.position,
              startDate: _args.startDate ? new Date(_args.startDate).getTime() : Date.now(),
              createdBy: userId,
            };

            const employee = await this.employeeService.create(tenantId, createEmployeeDto, userId);
            return `Nhân viên "${employee.employeeName || 'N/A'}" đã được tạo thành công với ID: ${employee.id}`;
          } catch (error) {
            return `Tạo nhân viên thất bại: ${error.message}`;
          }
        },
        {
          name: 'create_employee',
          description: 'Tạo nhân viên mới',
          schema: z.object({
            fullName: z.string().nonempty().describe('Họ và tên đầy đủ'),
            email: z.string().email().describe('Email nhân viên'),
            phoneNumber: z.string().optional().describe('Số điện thoại'),
            departmentId: z.number().describe('ID phòng ban'),
            position: z.string().optional().describe('Chức vụ'),
            startDate: z.string().optional().describe('Ngày bắt đầu làm việc (YYYY-MM-DD)'),
          }),
        }
      ),

      // Cập nhật thông tin nhân viên
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');
            const userId = parseInt(config?.configurable?.['userId'] || '0');

            const updateEmployeeDto = {
              employeeName: _args.fullName,
              email: _args.email,
              phoneNumber: _args.phoneNumber,
              departmentId: _args.departmentId,
              position: _args.position,
              status: _args.status as EmployeeStatus,
              updatedBy: userId,
            };

            const employee = await this.employeeService.update(tenantId, _args.employeeId, updateEmployeeDto, userId);
            return `Thông tin nhân viên "${employee.employeeName || 'N/A'}" đã được cập nhật thành công`;
          } catch (error) {
            return `Cập nhật thông tin nhân viên thất bại: ${error.message}`;
          }
        },
        {
          name: 'update_employee',
          description: 'Cập nhật thông tin nhân viên',
          schema: z.object({
            employeeId: z.number().describe('ID nhân viên'),
            fullName: z.string().optional().describe('Họ và tên đầy đủ'),
            email: z.string().email().optional().describe('Email nhân viên'),
            phoneNumber: z.string().optional().describe('Số điện thoại'),
            departmentId: z.number().optional().describe('ID phòng ban'),
            position: z.string().optional().describe('Chức vụ'),
            status: z.enum(['active', 'inactive', 'on_leave']).optional().describe('Trạng thái nhân viên'),
          }),
        }
      ),

      // === USER MANAGEMENT TOOLS ===
      // TODO: Tạm thời comment out do User entity chưa có đầy đủ fields
      // Cần cập nhật User entity để phù hợp với database schema

      /*
      // Lấy danh sách người dùng
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');

            const result = await this.userService.findAllUsers(tenantId, {
              page: _args.page || 1,
              limit: _args.limit || 10,
              search: _args.search,
              status: _args.status ? (_args.status as UserStatus) : undefined,
              departmentId: _args.departmentId,
              hasEmployee: _args.hasEmployee,
              userType: _args.userType,
            });

            return `Tìm thấy ${result.items.length} người dùng (tổng ${result.meta.totalItems}):\n${JSON.stringify(result.items, null, 2)}`;
          } catch (error) {
            return `Lấy danh sách người dùng thất bại: ${error.message}`;
          }
        },
        {
          name: 'get_user_list',
          description: 'Lấy danh sách người dùng với phân trang và bộ lọc',
          schema: z.object({
            page: z.number().optional().default(1).describe('Số trang'),
            limit: z.number().optional().default(10).describe('Số lượng kết quả trên mỗi trang'),
            search: z.string().optional().describe('Tìm kiếm theo tên, email, vị trí'),
            status: z.enum(['ACTIVE', 'INACTIVE']).optional().describe('Trạng thái người dùng'),
            departmentId: z.number().optional().describe('ID phòng ban'),
            hasEmployee: z.boolean().optional().describe('Lọc người dùng có liên kết với nhân viên'),
            userType: z.string().optional().describe('Loại người dùng'),
          }),
        }
      ),
      */

      // === EMPLOYEE STATISTICS TOOLS ===

      // Lấy thống kê tổng quan nhân viên
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');

            const overview = await this.employeeService.getOverview(tenantId);
            return `Thống kê tổng quan nhân viên:\n${JSON.stringify(overview, null, 2)}`;
          } catch (error) {
            return `Lấy thống kê tổng quan nhân viên thất bại: ${error.message}`;
          }
        },
        {
          name: 'get_employee_overview',
          description: 'Lấy thống kê tổng quan về nhân viên (tổng số, hoạt động, không hoạt động, mới trong tháng)',
          schema: z.object({}),
        }
      ),

      // Lấy thống kê phân bố nhân viên theo phòng ban
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');

            const distribution = await this.employeeService.getDepartmentDistribution(tenantId);
            return `Thống kê phân bố nhân viên theo phòng ban:\n${JSON.stringify(distribution, null, 2)}`;
          } catch (error) {
            return `Lấy thống kê phân bố theo phòng ban thất bại: ${error.message}`;
          }
        },
        {
          name: 'get_employee_department_distribution',
          description: 'Lấy thống kê phân bố nhân viên theo phòng ban với số lượng và phần trăm',
          schema: z.object({}),
        }
      ),

      // Lấy thống kê phân bố nhân viên theo loại hợp đồng
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');

            const distribution = await this.employeeService.getContractDistribution(tenantId);
            return `Thống kê phân bố nhân viên theo loại hợp đồng:\n${JSON.stringify(distribution, null, 2)}`;
          } catch (error) {
            return `Lấy thống kê phân bố theo hợp đồng thất bại: ${error.message}`;
          }
        },
        {
          name: 'get_employee_contract_distribution',
          description: 'Lấy thống kê phân bố nhân viên theo loại hợp đồng (toàn thời gian, bán thời gian, thực tập, v.v.)',
          schema: z.object({}),
        }
      ),

      // Lấy thống kê thâm niên nhân viên
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');

            const tenureStats = await this.employeeService.getTenureStats(tenantId);
            return `Thống kê thâm niên nhân viên:\n${JSON.stringify(tenureStats, null, 2)}`;
          } catch (error) {
            return `Lấy thống kê thâm niên thất bại: ${error.message}`;
          }
        },
        {
          name: 'get_employee_tenure_stats',
          description: 'Lấy thống kê thâm niên nhân viên (thâm niên trung bình, nhân viên thử việc sắp hết hạn, tỷ lệ tăng trưởng)',
          schema: z.object({}),
        }
      ),

      // === EMPLOYEE-USER MANAGEMENT TOOLS ===
      // TODO: Tạm thời comment out do User entity chưa có đầy đủ fields

      /*
      // Tạo nhân viên mới kèm tài khoản user
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');
            const userId = parseInt(config?.configurable?.['userId'] || '0');

            const createDto = {
              userInfo: {
                username: _args.username || _args.email.split('@')[0], // Tạo username từ email nếu không có
                email: _args.email,
                password: _args.password,
                fullName: _args.fullName || _args.employeeName,
              },
              employeeName: _args.employeeName,
              departmentId: _args.departmentId,
              jobTitle: _args.jobTitle,
            };

            const result = await this.employeeUserService.createEmployeeWithUser(tenantId, createDto, userId);
            return `Nhân viên và tài khoản user đã được tạo thành công:\n${JSON.stringify(result, null, 2)}`;
          } catch (error) {
            return `Tạo nhân viên kèm tài khoản user thất bại: ${error.message}`;
          }
        },
        {
          name: 'create_employee_with_user',
          description: 'Tạo nhân viên mới kèm tài khoản user',
          schema: z.object({
            employeeName: z.string().describe('Tên nhân viên'),
            email: z.string().email().describe('Email cho tài khoản user'),
            password: z.string().describe('Mật khẩu cho tài khoản user'),
            username: z.string().optional().describe('Tên đăng nhập (mặc định từ email)'),
            fullName: z.string().optional().describe('Họ tên đầy đủ (mặc định dùng employeeName)'),
            jobTitle: z.string().optional().describe('Chức danh công việc'),
            departmentId: z.number().describe('ID phòng ban'),
          }),
        }
      ),
      */

      // === PERMISSION MANAGEMENT TOOLS ===

      // Lấy danh sách tất cả quyền được nhóm theo module
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const permissionGroups = await this.employeePermissionService.getPermissionGroups();
            return `Danh sách quyền theo module:\n${JSON.stringify(permissionGroups, null, 2)}`;
          } catch (error) {
            return `Lấy danh sách quyền thất bại: ${error.message}`;
          }
        },
        {
          name: 'get_permission_groups',
          description: 'Lấy danh sách tất cả quyền được nhóm theo module',
          schema: z.object({}),
        }
      ),

      // Lấy quyền của một người dùng
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const userPermissions = await this.employeePermissionService.getUserPermissions(_args.userId);
            return `Quyền của người dùng ID ${_args.userId}:\n${JSON.stringify(userPermissions, null, 2)}`;
          } catch (error) {
            return `Lấy quyền người dùng thất bại: ${error.message}`;
          }
        },
        {
          name: 'get_user_permissions',
          description: 'Lấy danh sách quyền của một người dùng',
          schema: z.object({
            userId: z.number().describe('ID người dùng'),
          }),
        }
      ),

      // Cập nhật quyền cho nhân viên
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');
            const userId = parseInt(config?.configurable?.['userId'] || '0');

            const updateDto = {
              employeeId: _args.employeeId,
              permissionIds: _args.permissionIds,
            };

            const result = await this.employeePermissionService.updateEmployeePermission(tenantId, updateDto, userId);
            return `Quyền đã được cập nhật cho nhân viên thành công:\n${JSON.stringify(result, null, 2)}`;
          } catch (error) {
            return `Cập nhật quyền cho nhân viên thất bại: ${error.message}`;
          }
        },
        {
          name: 'update_employee_permissions',
          description: 'Cập nhật danh sách quyền cho nhân viên (thay thế tất cả quyền hiện tại)',
          schema: z.object({
            employeeId: z.number().describe('ID nhân viên'),
            permissionIds: z.array(z.number()).describe('Danh sách ID quyền mới'),
          }),
        }
      ),

      // Cập nhật vai trò cho nhân viên
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');
            const userId = parseInt(config?.configurable?.['userId'] || '0');

            const updateDto = {
              employeeId: _args.employeeId,
              roleIds: _args.roleIds,
            };

            const result = await this.employeePermissionService.updateEmployeeRole(tenantId, updateDto, userId);
            return `Vai trò đã được cập nhật cho nhân viên thành công:\n${JSON.stringify(result, null, 2)}`;
          } catch (error) {
            return `Cập nhật vai trò cho nhân viên thất bại: ${error.message}`;
          }
        },
        {
          name: 'update_employee_roles',
          description: 'Cập nhật danh sách vai trò cho nhân viên (thay thế tất cả vai trò hiện tại)',
          schema: z.object({
            employeeId: z.number().describe('ID nhân viên'),
            roleIds: z.array(z.number()).describe('Danh sách ID vai trò mới'),
          }),
        }
      ),

      // === ROLE MANAGEMENT TOOLS ===

      // Lấy danh sách vai trò
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const query = {
              page: _args.page || 1,
              limit: _args.limit || 10,
              search: _args.search,
              sortBy: _args.sortBy || 'name',
              sortDirection: (_args.sortDirection === 'DESC' ? SortDirection.DESC : SortDirection.ASC),
            };

            const result = await this.roleService.findAll(query);
            return `Tìm thấy ${result.items.length} vai trò (tổng ${result.meta.totalItems}):\n${JSON.stringify(result.items, null, 2)}`;
          } catch (error) {
            return `Lấy danh sách vai trò thất bại: ${error.message}`;
          }
        },
        {
          name: 'get_role_list',
          description: 'Lấy danh sách vai trò với phân trang và tìm kiếm',
          schema: z.object({
            page: z.number().optional().default(1).describe('Số trang'),
            limit: z.number().optional().default(10).describe('Số lượng kết quả trên mỗi trang'),
            search: z.string().optional().describe('Tìm kiếm theo tên vai trò'),
            sortBy: z.string().optional().default('name').describe('Trường sắp xếp'),
            sortDirection: z.enum(['ASC', 'DESC']).optional().default('ASC').describe('Hướng sắp xếp'),
          }),
        }
      ),

      // Lấy thông tin chi tiết vai trò
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const role = await this.roleService.findById(_args.roleId);
            return `Thông tin chi tiết vai trò:\n${JSON.stringify(role, null, 2)}`;
          } catch (error) {
            return `Lấy thông tin vai trò thất bại: ${error.message}`;
          }
        },
        {
          name: 'get_role_detail',
          description: 'Lấy thông tin chi tiết vai trò bao gồm danh sách quyền',
          schema: z.object({
            roleId: z.number().describe('ID vai trò'),
          }),
        }
      ),

      // Cập nhật quyền cho vai trò
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const userId = parseInt(config?.configurable?.['userId'] || '0');

            const updateDto = {
              roleId: _args.roleId,
              permissionIds: _args.permissionIds,
            };

            const result = await this.employeePermissionService.updateRolePermission(updateDto, userId);
            return `Quyền đã được cập nhật cho vai trò thành công:\n${JSON.stringify(result, null, 2)}`;
          } catch (error) {
            return `Cập nhật quyền cho vai trò thất bại: ${error.message}`;
          }
        },
        {
          name: 'update_role_permissions',
          description: 'Cập nhật danh sách quyền cho vai trò (thay thế tất cả quyền hiện tại)',
          schema: z.object({
            roleId: z.number().describe('ID vai trò'),
            permissionIds: z.array(z.number()).describe('Danh sách ID quyền mới'),
          }),
        }
      ),
    ];
  }
}
