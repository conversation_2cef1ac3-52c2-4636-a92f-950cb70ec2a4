import { Injectable } from '@nestjs/common';
import { EmployeeService } from '@modules/hrm/employees/services/employee.service';
import { tool, ToolRunnableConfig } from '@langchain/core/tools';
import { z } from 'zod';
import { EmployeeStatus } from '@modules/hrm/employees/enum/employee-status.enum';

/**
 * Employee Tools Provider
 * Cung cấp các tools liên quan đến quản lý nhân viên
 */
@Injectable()
export class EmployeeToolsProvider {
  constructor(private readonly employeeService: EmployeeService) {}

  /**
   * L<PERSON>y tất cả employee tools
   */
  getTools() {
    return [
      // Lấy thông tin nhân viên
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');
            const userId = parseInt(config?.configurable?.['userId'] || '0');

            let employee;

            if (_args.employeeId) {
              employee = await this.employeeService.findById(tenantId, _args.employeeId);
            } else if (_args.employeeName) {
              const employees = await this.employeeService.searchByName(tenantId, _args.employeeName);
              employee = employees[0];
            } else {
              employee = await this.employeeService.findByUserId(tenantId, userId);
            }

            if (!employee) {
              return 'Không tìm thấy nhân viên';
            }

            return `Thông tin nhân viên ${employee.employeeName || 'N/A'}:\n${JSON.stringify(employee, null, 2)}`;
          } catch (error) {
            return `Lấy thông tin nhân viên thất bại: ${error.message}`;
          }
        },
        {
          name: 'get_employee_info',
          description: 'Lấy thông tin chi tiết nhân viên',
          schema: z.object({
            employeeId: z.number().optional().describe('ID nhân viên'),
            employeeName: z.string().optional().describe('Tên nhân viên (tìm kiếm gần đúng)'),
            includeStats: z.boolean().optional().default(false).describe('Bao gồm thống kê hiệu suất'),
          }),
        }
      ),

      // Lấy danh sách nhân viên
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');

            const employees = await this.employeeService.getEmployeeList(tenantId, {
              departmentId: _args.departmentId,
              position: _args.position,
              status: _args.status,
              limit: _args.limit,
            });

            return `Tìm thấy ${employees.length} nhân viên:\n${JSON.stringify(employees, null, 2)}`;
          } catch (error) {
            return `Lấy danh sách nhân viên thất bại: ${error.message}`;
          }
        },
        {
          name: 'get_employee_list',
          description: 'Lấy danh sách nhân viên theo bộ lọc',
          schema: z.object({
            departmentId: z.number().optional().describe('ID phòng ban'),
            position: z.string().optional().describe('Chức vụ'),
            status: z.enum(['active', 'inactive', 'on_leave']).optional().default('active').describe('Trạng thái nhân viên'),
            limit: z.number().optional().default(20).describe('Số lượng kết quả tối đa'),
          }),
        }
      ),

      // Lấy nhân viên đi muộn
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');
            const date = _args.date || new Date().toISOString().split('T')[0];

            const lateEmployees = await this.employeeService.getLateEmployees(tenantId, new Date(date).getTime(), {
              departmentId: _args.departmentId,
              limit: _args.limit,
            });

            return `Tìm thấy ${lateEmployees.length} nhân viên đi muộn ngày ${date}:\n${JSON.stringify(lateEmployees, null, 2)}`;
          } catch (error) {
            return `Lấy danh sách nhân viên đi muộn thất bại: ${error.message}`;
          }
        },
        {
          name: 'get_late_employees',
          description: 'Lấy danh sách nhân viên đi muộn',
          schema: z.object({
            date: z.string().optional().describe('Ngày kiểm tra (YYYY-MM-DD), mặc định là hôm nay'),
            departmentId: z.number().optional().describe('ID phòng ban'),
            limit: z.number().optional().default(10).describe('Số lượng kết quả tối đa'),
          }),
        }
      ),

      // Tạo nhân viên mới
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');
            const userId = parseInt(config?.configurable?.['userId'] || '0');

            const createEmployeeDto = {
              employeeName: _args.fullName,
              email: _args.email,
              phoneNumber: _args.phoneNumber,
              departmentId: _args.departmentId,
              position: _args.position,
              startDate: _args.startDate ? new Date(_args.startDate).getTime() : Date.now(),
              createdBy: userId,
            };

            const employee = await this.employeeService.create(tenantId, createEmployeeDto, userId);
            return `Nhân viên "${employee.employeeName || 'N/A'}" đã được tạo thành công với ID: ${employee.id}`;
          } catch (error) {
            return `Tạo nhân viên thất bại: ${error.message}`;
          }
        },
        {
          name: 'create_employee',
          description: 'Tạo nhân viên mới',
          schema: z.object({
            fullName: z.string().nonempty().describe('Họ và tên đầy đủ'),
            email: z.string().email().describe('Email nhân viên'),
            phoneNumber: z.string().optional().describe('Số điện thoại'),
            departmentId: z.number().describe('ID phòng ban'),
            position: z.string().optional().describe('Chức vụ'),
            startDate: z.string().optional().describe('Ngày bắt đầu làm việc (YYYY-MM-DD)'),
          }),
        }
      ),

      // Cập nhật thông tin nhân viên
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');
            const userId = parseInt(config?.configurable?.['userId'] || '0');

            const updateEmployeeDto = {
              employeeName: _args.fullName,
              email: _args.email,
              phoneNumber: _args.phoneNumber,
              departmentId: _args.departmentId,
              position: _args.position,
              status: _args.status as EmployeeStatus,
              updatedBy: userId,
            };

            const employee = await this.employeeService.update(tenantId, _args.employeeId, updateEmployeeDto, userId);
            return `Thông tin nhân viên "${employee.employeeName || 'N/A'}" đã được cập nhật thành công`;
          } catch (error) {
            return `Cập nhật thông tin nhân viên thất bại: ${error.message}`;
          }
        },
        {
          name: 'update_employee',
          description: 'Cập nhật thông tin nhân viên',
          schema: z.object({
            employeeId: z.number().describe('ID nhân viên'),
            fullName: z.string().optional().describe('Họ và tên đầy đủ'),
            email: z.string().email().optional().describe('Email nhân viên'),
            phoneNumber: z.string().optional().describe('Số điện thoại'),
            departmentId: z.number().optional().describe('ID phòng ban'),
            position: z.string().optional().describe('Chức vụ'),
            status: z.enum(['active', 'inactive', 'on_leave']).optional().describe('Trạng thái nhân viên'),
          }),
        }
      ),
    ];
  }
}
