import { Injectable } from '@nestjs/common';
import { EmailService } from '@modules/email/services';
import { tool, ToolRunnableConfig } from '@langchain/core/tools';
import { z } from 'zod';

/**
 * Email Tools Provider
 * Cung cấp các tools liên quan đến email
 */
@Injectable()
export class EmailToolsProvider {
  constructor(private readonly emailService: EmailService) {}

  /**
   * <PERSON><PERSON>y tất cả email tools
   */
  getTools() {
    return [
      tool(
        async (
          _args,
        ): Promise<string> => {
          try {
            const { to, subject, body } = _args;
            await this.emailService.sendEmail({ to, subject, body });
            return 'Email sent successfully';
          } catch (error) {
            return `Failed to send email: ${error.message}`;
          }
        },
        {
          name: 'send_email',
          description: 'Gửi email tới người dùng',
          schema: z.object({
            to: z.string().nonempty().email().describe('Đ<PERSON>a chỉ email người nhận'),
            subject: z.string().nonempty().describe('Tiêu đề email'),
            body: z.string().nonempty().describe('Nội dung email'),
          }),
        }
      )
    ];
  }
}
