import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsInt, IsOptional, Min, IsArray } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { QueryDto } from '@/common/dto/query.dto';
import { TodoStatus } from '../../enum/todo-status.enum';
import { TodoPriority } from '../../enum/todo-priority.enum';

/**
 * DTO cho truy vấn danh sách công việc
 */
export class TodoQueryDto extends QueryDto {
  /**
   * Lọc theo trạng thái
   * @example "pending"
   */
  @ApiProperty({
    description: 'Lọc theo trạng thái',
    enum: TodoStatus,
    required: false,
    example: TodoStatus.PENDING,
  })
  @IsOptional()
  @IsEnum(TodoStatus, { message: 'Trạng thái không hợp lệ' })
  status?: TodoStatus;

  /**
   * Lọ<PERSON> theo mức độ ưu tiên
   * @example "medium"
   */
  @ApiProperty({
    description: 'Lọ<PERSON> theo mức độ ưu tiên',
    enum: TodoPriority,
    required: false,
    example: TodoPriority.MEDIUM,
  })
  @IsOptional()
  @IsEnum(TodoPriority, { message: 'Mức độ ưu tiên không hợp lệ' })
  priority?: TodoPriority;

  /**
   * Lọc theo người được giao
   * @example 1
   */
  @ApiProperty({
    description: 'Lọc theo người được giao',
    required: false,
    example: 1,
    type: Number,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'ID người được giao phải là số nguyên' })
  @Min(1, { message: 'ID người được giao phải lớn hơn 0' })
  assigneeId?: number;

  /**
   * Lọc theo dự án
   * @example 1
   */
  @ApiProperty({
    description: 'Lọc theo dự án',
    required: false,
    example: 1,
    type: Number,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'ID dự án phải là số nguyên' })
  @Min(1, { message: 'ID dự án phải lớn hơn 0' })
  categoryId?: number;

  /**
   * Lọc theo công việc cha (null để lấy công việc gốc)
   * @example 1
   */
  @ApiProperty({
    description: 'Lọc theo công việc cha (null để lấy công việc gốc)',
    required: false,
    example: 1,
    nullable: true,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'null') return null;
    if (!isNaN(parseInt(value))) return parseInt(value);
    return value;
  })
  parentId?: number | null;

  /**
   * Lọc theo danh sách tag IDs (có thể nhiều tags)
   * @example [1,2,3] hoặc "1,2,3"
   */
  @ApiProperty({
    description: 'Lọc theo danh sách tag IDs (có thể nhiều tags)',
    required: false,
    example: [1, 2, 3],
    type: [Number],
    isArray: true,
  })
  @IsOptional()
  @Transform(({ value }) => {
    // Xử lý trường hợp array từ query string: tagIds[]=1&tagIds[]=2
    if (Array.isArray(value)) {
      return value.map(id => parseInt(id)).filter(id => !isNaN(id));
    }
    // Xử lý trường hợp string: tagIds=1,2,3
    if (typeof value === 'string') {
      return value.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));
    }
    // Xử lý trường hợp single value: tagIds=1
    if (typeof value === 'number' || !isNaN(parseInt(value))) {
      return [parseInt(value)];
    }
    return value;
  })
  @IsArray()
  @IsInt({ each: true, message: 'Mỗi tag ID phải là số nguyên' })
  @Min(1, { each: true, message: 'Mỗi tag ID phải lớn hơn 0' })
  tagIds?: number[];
}
