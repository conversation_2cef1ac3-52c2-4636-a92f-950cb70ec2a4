import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@/modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@/modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { ApiResponseDto } from '@/common/response/api-response-dto';
import { SWAGGER_API_TAG } from '@/common/swagger/swagger.tags';
import { TodoTagService } from '../services/todo-tag.service';
import { CreateTodoTagDto } from '../dto/todo-tag/create-todo-tag.dto';
import { TodoTagResponseDto } from '../dto/todo-tag/todo-tag-response.dto';
import { RequirePermissionEnum } from '@/modules/auth/decorators/require-module-action.decorator';
import { Permission } from '@/modules/auth/enum/permission.enum';
import { PermissionsGuard } from '@/modules/auth/guards/permissions.guard';

/**
 * Controller xử lý các API liên quan đến tag của công việc
 */
@ApiTags(SWAGGER_API_TAG.TODOLISTS)
@ApiExtraModels(ApiResponseDto, TodoTagResponseDto)
@Controller('/todos/:todoId/tags')
@UseGuards(JwtUserGuard, PermissionsGuard)
@ApiBearerAuth('JWT-auth')
export class TodoTagController {
  constructor(private readonly todoTagService: TodoTagService) {}

  /**
   * Thêm tag vào công việc
   */
  @Post()
  @RequirePermissionEnum(Permission.PROJECT_UPDATE_TASK)
  @ApiOperation({ summary: 'Thêm tag vào công việc' })
  @ApiParam({ name: 'todoId', description: 'ID công việc', type: 'number' })
  @ApiResponse({
    status: 201,
    description: 'Tag đã được thêm vào công việc thành công',
    schema: ApiResponseDto.getSchema(TodoTagResponseDto),
  })
  async addTagToTodo(
    @Param('todoId', ParseIntPipe) todoId: number,
    @CurrentUser() user: JwtPayload,
    @Body() createTodoTagDto: CreateTodoTagDto,
  ): Promise<ApiResponseDto<TodoTagResponseDto>> {
    const tag = await this.todoTagService.addTagToTodo(
      Number(user.tenantId),
      todoId,
      user.id,
      createTodoTagDto,
    );
    return ApiResponseDto.created(tag);
  }

  /**
   * Lấy danh sách tag của công việc
   */
  @Get()
  @RequirePermissionEnum(Permission.PROJECT_VIEW_TASKS)
  @ApiOperation({ summary: 'Lấy danh sách tag của công việc' })
  @ApiParam({ name: 'todoId', description: 'ID công việc', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách tag của công việc',
    schema: ApiResponseDto.getArraySchema(TodoTagResponseDto),
  })
  async findTagsByTodoId(
    @CurrentUser() user: JwtPayload,
    @Param('todoId', ParseIntPipe) todoId: number,
  ): Promise<ApiResponseDto<TodoTagResponseDto[]>> {
    const tags = await this.todoTagService.findTagsByTodoId(
      Number(user.tenantId),
      todoId,
    );
    return ApiResponseDto.success(tags);
  }

  /**
   * Xóa tag khỏi công việc
   */
  @Delete(':tagId')
  @RequirePermissionEnum(Permission.PROJECT_UPDATE_TASK)
  @ApiOperation({ summary: 'Xóa tag khỏi công việc' })
  @ApiParam({ name: 'todoId', description: 'ID công việc', type: 'number' })
  @ApiParam({ name: 'tagId', description: 'ID tag', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Tag đã được xóa khỏi công việc thành công',
    schema: ApiResponseDto.getSchema(Boolean),
  })
  async removeTagFromTodo(
    @Param('todoId', ParseIntPipe) todoId: number,
    @Param('tagId', ParseIntPipe) tagId: number,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<boolean>> {
    await this.todoTagService.removeTagFromTodo(
      Number(user.tenantId),
      todoId,
      tagId,
      user.id,
    );
    return ApiResponseDto.deleted();
  }
}
