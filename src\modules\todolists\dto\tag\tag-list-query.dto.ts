import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';

/**
 * DTO cho truy vấn danh sách tag không phân trang
 */
export class TagListQueryDto {
  /**
   * Từ khóa tìm kiếm
   * @example "urgent"
   */
  @ApiProperty({
    description: 'Từ khóa tìm kiếm',
    example: 'urgent',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Từ khóa tìm kiếm phải là chuỗi' })
  search?: string;
}
