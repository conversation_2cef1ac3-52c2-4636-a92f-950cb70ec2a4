import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  ParseIntPipe,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
  ApiExtraModels,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@/modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@/modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { ApiResponseDto, PaginatedResult } from '@/common/response/api-response-dto';
import { DocumentFolderService } from '../services/document-folder.service';
import {
  CreateDocumentFolderDto,
  UpdateDocumentFolderDto,
  DocumentFolderQueryDto,
  DocumentFolderResponseDto,
} from '../dto/document-folder';
import { SWAGGER_API_TAG } from '@/common/swagger';

/**
 * Controller xử lý các API cho thư mục tài liệu
 */
@ApiTags(SWAGGER_API_TAG.DOCUMENT)
@ApiExtraModels(
  ApiResponseDto,
  PaginatedResult,
  DocumentFolderResponseDto,
)
@Controller('document-folders')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class DocumentFolderController {
  private readonly logger = new Logger(DocumentFolderController.name);

  constructor(private readonly documentFolderService: DocumentFolderService) {}

  /**
   * Tạo thư mục mới
   */
  @Post()
  @ApiOperation({
    summary: 'Tạo thư mục tài liệu mới',
    description: 'Tạo thư mục mới để tổ chức tài liệu',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Tạo thư mục thành công',
    schema: ApiResponseDto.getSchema(DocumentFolderResponseDto),
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu đầu vào không hợp lệ',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Thư mục cha không tồn tại',
  })
  async create(
    @Body() createDto: CreateDocumentFolderDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<DocumentFolderResponseDto>> {
    this.logger.log(
      `Tạo thư mục: ${createDto.name} (user: ${user.id}, tenant: ${user.tenantId})`,
    );

    const result = await this.documentFolderService.create(
      Number(user.tenantId),
      createDto,
      user.id,
    );

    return ApiResponseDto.created(result, 'Tạo thư mục thành công');
  }

  /**
   * Lấy danh sách thư mục
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách thư mục',
    description: 'Lấy danh sách thư mục với phân trang và bộ lọc',
  })
  @ApiQuery({ name: 'page', required: false, description: 'Số trang (mặc định: 1)' })
  @ApiQuery({ name: 'limit', required: false, description: 'Số lượng mỗi trang (mặc định: 10)' })
  @ApiQuery({ name: 'search', required: false, description: 'Từ khóa tìm kiếm' })
  @ApiQuery({ name: 'parentId', required: false, description: 'ID thư mục cha' })
  @ApiQuery({ name: 'isActive', required: false, description: 'Trạng thái hoạt động' })
  @ApiQuery({ name: 'sortBy', required: false, description: 'Trường sắp xếp' })
  @ApiQuery({ name: 'sortDirection', required: false, description: 'Hướng sắp xếp (ASC/DESC)' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy danh sách thư mục thành công',
    schema: ApiResponseDto.getPaginatedSchema(DocumentFolderResponseDto),
  })
  async findAll(
    @Query() queryDto: DocumentFolderQueryDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<PaginatedResult<DocumentFolderResponseDto>>> {
    this.logger.log(
      `Lấy danh sách thư mục (user: ${user.id}, tenant: ${user.tenantId})`,
    );

    const result = await this.documentFolderService.findAll(
      Number(user.tenantId),
      queryDto,
    );

    return ApiResponseDto.paginated(result, 'Lấy danh sách thư mục thành công');
  }

  /**
   * Lấy cây thư mục
   */
  @Get('tree')
  @ApiOperation({
    summary: 'Lấy cây thư mục',
    description: 'Lấy cấu trúc cây thư mục đầy đủ',
  })
  @ApiQuery({ name: 'includeDocuments', required: false, description: 'Bao gồm tài liệu trong thư mục' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy cây thư mục thành công',
    schema: ApiResponseDto.getArraySchema(DocumentFolderResponseDto),
  })
  async getTree(
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<DocumentFolderResponseDto[]>> {
    this.logger.log(
      `Lấy cây thư mục (user: ${user.id}, tenant: ${user.tenantId})`,
    );

    // TODO: Implement getTree method in DocumentFolderService
    // const result = await this.documentFolderService.getTree(
    //   Number(user.tenantId),
    //   includeDocuments,
    // );

    // Temporary return empty array until getTree is implemented
    const result: DocumentFolderResponseDto[] = [];

    return ApiResponseDto.success(result, 'Lấy cây thư mục thành công');
  }

  /**
   * Lấy chi tiết thư mục
   */
  @Get(':id')
  @ApiOperation({
    summary: 'Lấy chi tiết thư mục',
    description: 'Lấy thông tin chi tiết của một thư mục',
  })
  @ApiParam({ name: 'id', description: 'ID thư mục' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy chi tiết thư mục thành công',
    schema: ApiResponseDto.getSchema(DocumentFolderResponseDto),
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy thư mục',
  })
  async findById(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<DocumentFolderResponseDto | null>> {
    this.logger.log(
      `Lấy chi tiết thư mục ID: ${id} (user: ${user.id}, tenant: ${user.tenantId})`,
    );

    const result = await this.documentFolderService.findById(
      Number(user.tenantId),
      id,
    );

    if (!result) {
      return ApiResponseDto.success(null, 'Không tìm thấy thư mục');
    }

    return ApiResponseDto.success(result, 'Lấy chi tiết thư mục thành công');
  }

  /**
   * Cập nhật thư mục
   */
  @Put(':id')
  @ApiOperation({
    summary: 'Cập nhật thư mục',
    description: 'Cập nhật thông tin thư mục',
  })
  @ApiParam({ name: 'id', description: 'ID thư mục' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Cập nhật thư mục thành công',
    schema: ApiResponseDto.getSchema(DocumentFolderResponseDto),
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy thư mục',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu đầu vào không hợp lệ',
  })
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateDocumentFolderDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<DocumentFolderResponseDto | null>> {
    this.logger.log(
      `Cập nhật thư mục ID: ${id} (user: ${user.id}, tenant: ${user.tenantId})`,
    );

    const result = await this.documentFolderService.update(
      Number(user.tenantId),
      id,
      updateDto,
      user.id,
    );

    if (!result) {
      return ApiResponseDto.success(null, 'Không tìm thấy thư mục');
    }

    return ApiResponseDto.updated(result, 'Cập nhật thư mục thành công');
  }

  /**
   * Xóa thư mục
   */
  @Delete(':id')
  @ApiOperation({
    summary: 'Xóa thư mục',
    description: 'Xóa thư mục (soft delete)',
  })
  @ApiParam({ name: 'id', description: 'ID thư mục' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Xóa thư mục thành công',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy thư mục',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Thư mục có chứa thư mục con hoặc tài liệu',
  })
  async delete(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<boolean>> {
    this.logger.log(
      `Xóa thư mục ID: ${id} (user: ${user.id}, tenant: ${user.tenantId})`,
    );

    const result = await this.documentFolderService.delete(
      Number(user.tenantId),
      id,
    );

    if (!result) {
      return ApiResponseDto.success(false, 'Không tìm thấy thư mục hoặc thư mục có chứa dữ liệu');
    }

    return ApiResponseDto.deleted(true, 'Xóa thư mục thành công');
  }
}
