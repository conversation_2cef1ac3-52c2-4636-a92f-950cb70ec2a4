# Exception Handling trong RedAI Backend

## Giới thiệu

Module này cung cấp một hệ thống xử lý exception toàn diện cho ứng dụng RedAI Backend. <PERSON><PERSON> giúp bắt và xử lý tất cả các exception x<PERSON>y ra trong Controller và Service, chuyển đổi chúng thành response chuẩn và ghi log chi tiết.

## C<PERSON>c thành phần chính

### 1. AppException

`AppException` là một custom exception class kế thừa từ `HttpException` của NestJS. Nó cho phép bạn ném lỗi với mã lỗi, thông báo và thông tin chi tiết.

```typescript
throw new AppException(
  ErrorCode.RESOURCE_NOT_FOUND,
  'User not found',
  { userId: 123 }
);
```

### 2. ErrorCode

`ErrorCode` là một enum định nghĩa các mã lỗi trong hệ thống. Mỗi mã lỗi tương ứng với một loại lỗi cụ thể.

```typescript
export enum ErrorCode {
  RESOURCE_NOT_FOUND = 'RESOURCE_NOT_FOUND',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  // ...
}
```

### 3. GlobalExceptionFilter

`GlobalExceptionFilter` là một filter bắt tất cả các exception trong ứng dụng và chuyển đổi chúng thành response chuẩn.

### 4. ErrorHandlingInterceptor

`ErrorHandlingInterceptor` là một interceptor bắt lỗi trong các Promise và Observable, chuyển đổi các lỗi không xác định thành `AppException`.

## Cách sử dụng

### 1. Ném lỗi trong Service

```typescript
import { Injectable } from '@nestjs/common';
import { AppException, ErrorCode } from '@common/exceptions';

@Injectable()
export class UserService {
  async findById(id: string) {
    const user = await this.userRepository.findOne(id);
    
    if (!user) {
      throw new AppException(
        ErrorCode.RESOURCE_NOT_FOUND,
        `User with id ${id} not found`,
        { id }
      );
    }
    
    return user;
  }
}
```

### 2. Xử lý lỗi trong try/catch

```typescript
try {
  // Thực hiện một thao tác có thể gây lỗi
  await this.someService.doSomething();
} catch (error) {
  // Nếu đã là AppException thì không cần wrap lại
  if (error instanceof AppException) {
    throw error;
  }
  
  // Wrap lỗi trong AppException
  throw new AppException(
    ErrorCode.EXTERNAL_SERVICE_ERROR,
    'Failed to perform operation',
    { originalError: error.message }
  );
}
```

## Cấu trúc Response Lỗi

Khi xảy ra lỗi, API sẽ trả về response với cấu trúc sau:

```json
{
  "code": "RESOURCE_NOT_FOUND",
  "message": "User with id 123 not found",
  "details": {
    "id": 123
  },
  "timestamp": "2023-05-01T12:34:56.789Z",
  "path": "/users/123",
  "requestId": "550e8400-e29b-41d4-a716-446655440000"
}
```

## Các loại lỗi và HTTP Status Code tương ứng

| ErrorCode | HTTP Status Code | Mô tả |
|-----------|------------------|-------|
| UNAUTHORIZED | 401 | Không có quyền truy cập |
| FORBIDDEN | 403 | Không đủ quyền |
| RESOURCE_NOT_FOUND | 404 | Không tìm thấy tài nguyên |
| VALIDATION_ERROR | 400 | Lỗi validation |
| DUPLICATE_ENTRY | 409 | Dữ liệu đã tồn tại |
| INTERNAL_SERVER_ERROR | 500 | Lỗi server không xác định |
| DATABASE_ERROR | 500 | Lỗi cơ sở dữ liệu |
| EXTERNAL_SERVICE_ERROR | 500 | Lỗi dịch vụ bên ngoài |

## Ví dụ

Xem các ví dụ về cách sử dụng AppException tại:
- `src/common/examples/exception-example.service.ts`
- `src/common/examples/exception-example.controller.ts`

Bạn có thể test các ví dụ này bằng cách gọi các API sau:
- `GET /exception-examples/not-found/:id` - Ví dụ về lỗi resource not found
- `POST /exception-examples/validate` - Ví dụ về lỗi validation
- `POST /exception-examples/payment` - Ví dụ về lỗi business rule
- `GET /exception-examples/unexpected-error` - Ví dụ về lỗi không xác định
