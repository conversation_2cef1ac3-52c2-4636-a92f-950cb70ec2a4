import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { Todo } from '../entities/todo.entity';
import { PaginatedResult } from '@/common/response/api-response-dto';
import { TodoQueryDto } from '../dto/todo/todo-query.dto';
import { DeadlineStatisticsQueryDto } from '../dto/todo/deadline-statistics-query.dto';
import { TodoStatus } from '../enum/todo-status.enum';

/**
 * Repository cho entity Todo
 */
@Injectable()
export class TodoRepository {
  constructor(
    @InjectRepository(Todo)
    private readonly repository: Repository<Todo>,
  ) {}

  /**
   * Tạo công việc mới
   * @param tenantId ID tenant (required for tenant isolation)
   * @param data Dữ liệu công việc
   * @returns Công việc đã tạo
   */
  async create(tenantId: number, data: Partial<Todo>): Promise<Todo> {
    const todo = this.repository.create({ ...data, tenantId });
    return this.repository.save(todo);
  }

  /**
   * Tìm tất cả công việc với phân trang và lọc
   * @param tenantId ID tenant (required for tenant isolation)
   * @param query Tham số truy vấn
   * @returns Danh sách công việc đã phân trang
   */
  async findAll(
    tenantId: number,
    query: TodoQueryDto,
  ): Promise<PaginatedResult<Todo>> {
    const {
      page = 1,
      limit = 10,
      search,
      sortBy = 'createdAt',
      sortDirection = 'DESC',
      status,
      priority,
      assigneeId,
      categoryId,
      parentId,
    } = query;

    const queryBuilder = this.repository.createQueryBuilder('todo');

    // Add tenantId filtering - REQUIRED for tenant isolation
    queryBuilder.andWhere('todo.tenantId = :tenantId', { tenantId });

    // Áp dụng bộ lọc trạng thái nếu được cung cấp
    if (status) {
      queryBuilder.andWhere('todo.status = :status', { status });
    }

    // Áp dụng bộ lọc mức độ ưu tiên nếu được cung cấp
    if (priority) {
      queryBuilder.andWhere('todo.priority = :priority', { priority });
    }

    // Áp dụng bộ lọc người được giao nếu được cung cấp
    if (assigneeId) {
      queryBuilder.andWhere('todo.assigneeId = :assigneeId', { assigneeId });
    }

    // Áp dụng bộ lọc dự án nếu được cung cấp
    if (categoryId) {
      queryBuilder.andWhere('todo.categoryId = :categoryId', { categoryId });
    }

    // Áp dụng bộ lọc công việc cha nếu được cung cấp
    if (parentId !== undefined) {
      if (parentId === null) {
        queryBuilder.andWhere('todo.parentId IS NULL');
      } else {
        queryBuilder.andWhere('todo.parentId = :parentId', { parentId });
      }
    }

    // Áp dụng bộ lọc tìm kiếm nếu được cung cấp
    if (search) {
      queryBuilder.andWhere(
        'todo.title ILIKE :search OR todo.description ILIKE :search',
        {
          search: `%${search}%`,
        },
      );
    }

    // Áp dụng sắp xếp
    queryBuilder.orderBy(`todo.${sortBy}`, sortDirection);

    // Áp dụng phân trang
    const [items, totalItems] = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Tìm tất cả công việc với phân trang, lọc và bao gồm tags
   * @param tenantId ID tenant (required for tenant isolation)
   * @param query Tham số truy vấn
   * @returns Danh sách công việc đã phân trang với tags
   */
  async findAllWithTags(
    tenantId: number,
    query: TodoQueryDto,
  ): Promise<PaginatedResult<any>> {
    const {
      page = 1,
      limit = 10,
      search,
      sortBy = 'createdAt',
      sortDirection = 'DESC',
      status,
      priority,
      assigneeId,
      categoryId,
      parentId,
      tagIds,
    } = query;



    const queryBuilder = this.repository.createQueryBuilder('todo');

    // Add tenantId filtering - REQUIRED for tenant isolation
    queryBuilder.andWhere('todo.tenantId = :tenantId', { tenantId });

    // Left join với todo_tags và tags để lấy thông tin tags
    queryBuilder.leftJoin(
      'todo_tags',
      'todoTag',
      'todoTag.todo_id = todo.id AND todoTag.tenant_id = :tenantId',
      { tenantId },
    );

    queryBuilder.leftJoin(
      'tags',
      'tag',
      'tag.id = todoTag.tag_id AND tag.tenant_id = :tenantIdStr',
      { tenantIdStr: tenantId.toString() },
    );

    // Select tất cả fields của todo và tag
    queryBuilder.select([
      'todo.id',
      'todo.title',
      'todo.description',
      'todo.assigneeId',
      'todo.status',
      'todo.priority',
      'todo.expectedStars',
      'todo.awardedStars',
      'todo.createdBy',
      'todo.createdAt',
      'todo.updatedAt',
      'todo.completedAt',
      'todo.deadline',
      'todo.categoryId',
      'todo.parentId',
      'tag.id as tag_id',
      'tag.name as tag_name',
      'tag.tenant_id as tag_tenant_id',
    ]);

    // Áp dụng bộ lọc trạng thái nếu được cung cấp
    if (status) {
      queryBuilder.andWhere('todo.status = :status', { status });
    }

    // Áp dụng bộ lọc mức độ ưu tiên nếu được cung cấp
    if (priority) {
      queryBuilder.andWhere('todo.priority = :priority', { priority });
    }

    // Áp dụng bộ lọc người được giao nếu được cung cấp
    if (assigneeId) {
      queryBuilder.andWhere('todo.assigneeId = :assigneeId', { assigneeId });
    }

    // Áp dụng bộ lọc dự án nếu được cung cấp
    if (categoryId) {
      queryBuilder.andWhere('todo.categoryId = :categoryId', { categoryId });
    }

    // Áp dụng bộ lọc công việc cha nếu được cung cấp
    if (parentId !== undefined) {
      if (parentId === null) {
        queryBuilder.andWhere('todo.parentId IS NULL');
      } else {
        queryBuilder.andWhere('todo.parentId = :parentId', { parentId });
      }
    }

    // Áp dụng bộ lọc tìm kiếm nếu được cung cấp
    if (search) {
      queryBuilder.andWhere(
        'todo.title ILIKE :search OR todo.description ILIKE :search',
        {
          search: `%${search}%`,
        },
      );
    }

    // Áp dụng bộ lọc theo tags nếu được cung cấp
    if (tagIds && tagIds.length > 0) {
      queryBuilder.andWhere(
        'todo.id IN (SELECT DISTINCT tt.todo_id FROM todo_tags tt WHERE tt.tag_id IN (:...tagIds) AND tt.tenant_id = :tenantId)',
        { tagIds, tenantId },
      );
    }

    // Áp dụng sắp xếp
    queryBuilder.orderBy(`todo.${sortBy}`, sortDirection);

    // Lấy tất cả kết quả trước khi phân trang để group tags
    const allResults = await queryBuilder.getRawMany();

    // Group theo todo và tập hợp tags
    const todoMap = new Map();
    allResults.forEach((row) => {
      const todoId = row.todo_id;
      if (!todoMap.has(todoId)) {
        todoMap.set(todoId, {
          id: row.todo_id,
          title: row.todo_title,
          description: row.todo_description,
          assigneeId: row.todo_assigneeId,
          status: row.todo_status,
          priority: row.todo_priority,
          expectedStars: row.todo_expectedStars,
          awardedStars: row.todo_awardedStars,
          createdBy: row.todo_createdBy,
          createdAt: row.todo_createdAt,
          updatedAt: row.todo_updatedAt,
          completedAt: row.todo_completedAt,
          deadline: row.todo_deadline,
          categoryId: row.todo_categoryId,
          parentId: row.todo_parentId,
          tags: [],
        });
      }

      // Thêm tag nếu có
      if (row.tag_id) {
        const todo = todoMap.get(todoId);
        const existingTag = todo.tags.find((tag: any) => tag.id === row.tag_id);
        if (!existingTag) {
          todo.tags.push({
            id: row.tag_id,
            name: row.tag_name,
            tenantId: row.tag_tenant_id,
          });
        }
      }
    });

    // Chuyển Map thành array và áp dụng phân trang
    const allTodos = Array.from(todoMap.values());
    const totalItems = allTodos.length;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const items = allTodos.slice(startIndex, endIndex);

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Tìm công việc theo ID
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID công việc
   * @returns Công việc hoặc null nếu không tìm thấy
   */
  async findById(tenantId: number, id: number): Promise<Todo | null> {
    return this.repository.findOne({
      where: { id, tenantId },
    });
  }

  /**
   * Tìm công việc con của một công việc
   * @param tenantId ID tenant (required for tenant isolation)
   * @param parentId ID công việc cha
   * @param query Tham số truy vấn
   * @returns Danh sách công việc con đã phân trang
   */
  async findSubtasks(
    tenantId: number,
    parentId: number,
    query: TodoQueryDto,
  ): Promise<PaginatedResult<Todo>> {
    const {
      page = 1,
      limit = 10,
      search,
      sortBy = 'createdAt',
      sortDirection = 'DESC',
      status,
    } = query;

    const queryBuilder = this.repository.createQueryBuilder('todo');
    queryBuilder.where(
      'todo.parentId = :parentId AND todo.tenantId = :tenantId',
      { parentId, tenantId },
    );

    // Áp dụng bộ lọc trạng thái nếu được cung cấp
    if (status) {
      queryBuilder.andWhere('todo.status = :status', { status });
    }

    // Áp dụng bộ lọc tìm kiếm nếu được cung cấp
    if (search) {
      queryBuilder.andWhere(
        'todo.title ILIKE :search OR todo.description ILIKE :search',
        {
          search: `%${search}%`,
        },
      );
    }

    // Áp dụng sắp xếp
    queryBuilder.orderBy(`todo.${sortBy}`, sortDirection);

    // Áp dụng phân trang
    const [items, totalItems] = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Cập nhật công việc
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID công việc
   * @param data Dữ liệu cập nhật
   * @returns Công việc đã cập nhật
   */
  async update(
    tenantId: number,
    id: number,
    data: Partial<Todo>,
  ): Promise<Todo | null> {
    await this.repository.update({ id, tenantId }, data);
    return this.findById(tenantId, id);
  }

  /**
   * Cập nhật trạng thái công việc
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID công việc
   * @param status Trạng thái mới
   * @returns Công việc đã cập nhật
   */
  async updateStatus(
    tenantId: number,
    id: number,
    status: TodoStatus,
  ): Promise<Todo | null> {
    const now = Date.now();
    const data: Partial<Todo> = {
      status,
      updatedAt: now,
    };

    // Nếu trạng thái là COMPLETED, cập nhật completedAt
    if (status === TodoStatus.COMPLETED) {
      data.completedAt = now;
    }

    await this.repository.update({ id, tenantId }, data);
    return this.findById(tenantId, id);
  }

  /**
   * Cập nhật điểm đánh giá cho công việc
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID công việc
   * @param awardedStars Số sao đánh giá (1-5)
   * @returns Công việc đã cập nhật
   */
  async updateScore(
    tenantId: number,
    id: number,
    awardedStars: number,
  ): Promise<Todo | null> {
    const now = Date.now();
    await this.repository.update(
      { id, tenantId },
      {
        awardedStars,
        updatedAt: now,
        status: TodoStatus.APPROVED, // Cập nhật trạng thái thành APPROVED sau khi chấm điểm
      },
    );
    return this.findById(tenantId, id);
  }

  /**
   * Xóa công việc
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID công việc
   * @returns Kết quả xóa
   */
  async delete(tenantId: number, id: number): Promise<boolean> {
    const result = await this.repository.delete({ id, tenantId });
    return (
      result.affected !== null &&
      result.affected !== undefined &&
      result.affected > 0
    );
  }

  /**
   * Tìm nhiều công việc theo danh sách ID
   * @param tenantId ID tenant (required for tenant isolation)
   * @param ids Danh sách ID công việc
   * @returns Danh sách công việc tìm thấy
   */
  async findByIds(tenantId: number, ids: number[]): Promise<Todo[]> {
    if (ids.length === 0) return [];

    return this.repository.find({
      where: {
        id: In(ids),
        tenantId,
      },
    });
  }

  /**
   * Xóa nhiều công việc
   * @param tenantId ID tenant (required for tenant isolation)
   * @param ids Danh sách ID công việc cần xóa
   * @returns Số lượng công việc đã xóa thành công
   */
  async bulkDelete(tenantId: number, ids: number[]): Promise<number> {
    if (ids.length === 0) return 0;

    const result = await this.repository.delete({
      id: In(ids),
      tenantId,
    });

    return result.affected ?? 0;
  }

  /**
   * Tìm công việc có deadline với thông tin mở rộng cho thống kê
   * @param tenantId ID tenant (required for tenant isolation)
   * @param query Tham số truy vấn
   * @returns Danh sách công việc có deadline với thông tin mở rộng
   */
  async findTodosWithDeadline(
    tenantId: number,
    query: DeadlineStatisticsQueryDto,
  ): Promise<any[]> {
    const queryBuilder = this.repository.createQueryBuilder('todo');

    // Add tenantId filtering - REQUIRED for tenant isolation
    queryBuilder.andWhere('todo.tenantId = :tenantId', { tenantId });

    // Chỉ lấy công việc có deadline
    queryBuilder.andWhere('todo.deadline IS NOT NULL');

    // Left join với projects để lấy tên dự án
    queryBuilder.leftJoin(
      'projects',
      'project',
      'project.id = todo.category_id AND project.tenant_id = :tenantId',
      { tenantId },
    );

    // Left join với users để lấy tên người được giao (giả sử có bảng users)
    // Tạm thời comment vì chưa có module users
    // queryBuilder.leftJoin(
    //   'users',
    //   'assignee',
    //   'assignee.id = todo.assignee_id',
    // );

    // Left join với todo_tags và tags để lấy thông tin tags
    queryBuilder.leftJoin(
      'todo_tags',
      'todoTag',
      'todoTag.todo_id = todo.id AND todoTag.tenant_id = :tenantId',
      { tenantId },
    );

    queryBuilder.leftJoin(
      'tags',
      'tag',
      'tag.id = todoTag.tag_id AND tag.tenant_id = :tenantIdStr',
      { tenantIdStr: tenantId.toString() },
    );

    // Select các trường cần thiết
    queryBuilder.select([
      'todo.id',
      'todo.title',
      'todo.description',
      'todo.assigneeId',
      'todo.categoryId',
      'todo.deadline',
      'todo.status',
      'todo.priority',
      'project.title as projectName',
      // 'assignee.name as assigneeName', // Tạm thời comment
      'STRING_AGG(tag.name, \',\') as tags',
    ]);

    // Áp dụng các bộ lọc
    if (query.projectId) {
      queryBuilder.andWhere('todo.categoryId = :projectId', {
        projectId: query.projectId,
      });
    }

    if (query.assigneeId) {
      queryBuilder.andWhere('todo.assigneeId = :assigneeId', {
        assigneeId: query.assigneeId,
      });
    }

    if (query.startDate) {
      queryBuilder.andWhere('todo.deadline >= :startDate', {
        startDate: query.startDate,
      });
    }

    if (query.endDate) {
      queryBuilder.andWhere('todo.deadline <= :endDate', {
        endDate: query.endDate,
      });
    }

    if (query.tags && query.tags.length > 0) {
      queryBuilder.andWhere('tag.name IN (:...tags)', { tags: query.tags });
    }

    // Group by để tránh duplicate khi có nhiều tags
    queryBuilder
      .groupBy('todo.id')
      .addGroupBy('todo.title')
      .addGroupBy('todo.description')
      .addGroupBy('todo.assigneeId')
      .addGroupBy('todo.categoryId')
      .addGroupBy('todo.deadline')
      .addGroupBy('todo.status')
      .addGroupBy('todo.priority')
      .addGroupBy('project.title');
      // .addGroupBy('assignee.name') // Tạm thời comment

    // Sắp xếp theo deadline
    queryBuilder.orderBy('todo.deadline', 'ASC');

    const rawResults = await queryBuilder.getRawMany();

    // Map kết quả để có format phù hợp
    return rawResults.map((row) => ({
      id: row.todo_id,
      title: row.todo_title,
      description: row.todo_description,
      assigneeId: row.todo_assigneeid,
      assigneeName: null, // Tạm thời null, sẽ cập nhật khi có module users
      categoryId: row.todo_categoryid,
      projectName: row.projectname,
      deadline: row.todo_deadline,
      status: row.todo_status,
      priority: row.todo_priority,
      tags: row.tags ? row.tags.split(',') : null,
    }));
  }
}
