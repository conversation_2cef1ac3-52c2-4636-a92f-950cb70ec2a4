import { Injectable } from '@nestjs/common';
import { DepartmentService } from '@modules/hrm/org-units/services/department.service';
import { DepartmentMembersService } from '@modules/hrm/org-units/services/department-members.service';
import { tool, ToolRunnableConfig } from '@langchain/core/tools';
import { z } from 'zod';

/**
 * Department Tools Provider
 * Cung cấp các tools liên quan đến quản lý phòng ban
 */
@Injectable()
export class DepartmentToolsProvider {
  constructor(
    private readonly departmentService: DepartmentService,
    private readonly departmentMembersService: DepartmentMembersService,
  ) {}

  /**
   * Lấy tất cả department tools
   */
  getTools() {
    return [
      // Tạo phòng ban mới
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');
            const userId = parseInt(config?.configurable?.['userId'] || '0');

            const createDepartmentDto = {
              name: _args.name,
              description: _args.description,
              managerId: _args.managerId,
              parentId: _args.parentId,
              code: _args.code,
              createdBy: userId,
            };

            const department = await this.departmentService.create(tenantId, createDepartmentDto);
            return `Phòng ban "${department.name}" đã được tạo thành công với ID: ${department.id}`;
          } catch (error) {
            return `Tạo phòng ban thất bại: ${error.message}`;
          }
        },
        {
          name: 'create_department',
          description: 'Tạo phòng ban mới',
          schema: z.object({
            name: z.string().nonempty().describe('Tên phòng ban'),
            description: z.string().optional().describe('Mô tả phòng ban'),
            managerId: z.number().optional().describe('ID trưởng phòng'),
            parentId: z.number().optional().describe('ID phòng ban cha'),
            code: z.string().optional().describe('Mã phòng ban'),
          }),
        }
      ),

      // Lấy danh sách phòng ban
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');

            const departments = await this.departmentService.findAll(tenantId, {
              parentId: _args.parentId,
              managerId: _args.managerId,
              limit: _args.limit,
              page: 1,
            });

            return `Tìm thấy ${departments.items?.length || 0} phòng ban:\n${JSON.stringify(departments.items, null, 2)}`;
          } catch (error) {
            return `Lấy danh sách phòng ban thất bại: ${error.message}`;
          }
        },
        {
          name: 'get_department_list',
          description: 'Lấy danh sách phòng ban',
          schema: z.object({
            parentId: z.number().optional().describe('ID phòng ban cha (lấy phòng ban con)'),
            managerId: z.number().optional().describe('ID trưởng phòng'),
            includeMembers: z.boolean().optional().default(false).describe('Bao gồm danh sách thành viên'),
            limit: z.number().optional().default(20).describe('Số lượng kết quả tối đa'),
          }),
        }
      ),

      // Lấy chi tiết phòng ban
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');

            const department = await this.departmentService.findById(tenantId, _args.departmentId);
            return `Chi tiết phòng ban "${department.name}":\n${JSON.stringify(department, null, 2)}`;
          } catch (error) {
            return `Lấy chi tiết phòng ban thất bại: ${error.message}`;
          }
        },
        {
          name: 'get_department_details',
          description: 'Lấy chi tiết phòng ban',
          schema: z.object({
            departmentId: z.number().describe('ID phòng ban'),
          }),
        }
      ),

      // Lấy danh sách thành viên phòng ban
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');

            const members = await this.departmentMembersService.getDepartmentMembers(
              tenantId,
              _args.departmentId
            );

            return `Tìm thấy ${members.totalMembers} thành viên trong phòng ban "${members.department.name}":\n${JSON.stringify(members, null, 2)}`;
          } catch (error) {
            return `Lấy danh sách thành viên phòng ban thất bại: ${error.message}`;
          }
        },
        {
          name: 'get_department_members',
          description: 'Lấy danh sách thành viên phòng ban',
          schema: z.object({
            departmentId: z.number().describe('ID phòng ban'),
            includeSubDepartments: z.boolean().optional().default(false).describe('Bao gồm thành viên phòng ban con'),
          }),
        }
      ),

      // Cập nhật phòng ban
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');
            const userId = parseInt(config?.configurable?.['userId'] || '0');

            const updateDepartmentDto = {
              name: _args.name,
              description: _args.description,
              managerId: _args.managerId,
              code: _args.code,
              updatedBy: userId,
            };

            const department = await this.departmentService.update(tenantId, _args.departmentId, updateDepartmentDto);
            return `Phòng ban "${department.name}" đã được cập nhật thành công`;
          } catch (error) {
            return `Cập nhật phòng ban thất bại: ${error.message}`;
          }
        },
        {
          name: 'update_department',
          description: 'Cập nhật thông tin phòng ban',
          schema: z.object({
            departmentId: z.number().describe('ID phòng ban'),
            name: z.string().optional().describe('Tên phòng ban mới'),
            description: z.string().optional().describe('Mô tả phòng ban mới'),
            managerId: z.number().optional().describe('ID trưởng phòng mới'),
            code: z.string().optional().describe('Mã phòng ban mới'),
          }),
        }
      ),

      // Xóa phòng ban
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');

            const result = await this.departmentService.delete(tenantId, _args.departmentId);
            return result ? `Phòng ban đã được xóa thành công` : `Xóa phòng ban thất bại`;
          } catch (error) {
            return `Xóa phòng ban thất bại: ${error.message}`;
          }
        },
        {
          name: 'delete_department',
          description: 'Xóa phòng ban',
          schema: z.object({
            departmentId: z.number().describe('ID phòng ban'),
          }),
        }
      ),
    ];
  }
}
