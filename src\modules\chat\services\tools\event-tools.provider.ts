import { Injectable } from '@nestjs/common';
import { TodoCommentService } from '@modules/todolists/services/todo-comment.service';
import { tool, ToolRunnableConfig } from '@langchain/core/tools';
import { z } from 'zod';

/**
 * Event Tools Provider
 * Cung cấp các tools liên quan đến sự kiện hệ thống và lịch sử thay đổi
 */
@Injectable()
export class EventToolsProvider {
  constructor(
    private readonly todoCommentService: TodoCommentService,
  ) {}

  /**
   * Lấy tất cả event tools
   */
  getTools() {
    return [
      // L<PERSON>y lịch sử sự kiện của công việc
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');

            // Lấy tất cả comment của todo, bao gồm system events
            const comments = await this.todoCommentService.findByTodoId(tenantId, _args.todoId);
            
            // Lọc chỉ lấy system events
            const systemEvents = comments.filter(comment => comment.isSystemEvent);

            return `Tìm thấy ${systemEvents.length} sự kiện hệ thống cho công việc:\n${JSON.stringify(systemEvents, null, 2)}`;
          } catch (error) {
            return `Lấy lịch sử sự kiện thất bại: ${error.message}`;
          }
        },
        {
          name: 'get_todo_events',
          description: 'Lấy lịch sử sự kiện của công việc',
          schema: z.object({
            todoId: z.number().describe('ID công việc'),
            eventType: z.enum(['status_changed', 'collaborator_changed', 'assignee_changed', 'all']).optional().default('all').describe('Loại sự kiện'),
            limit: z.number().optional().default(20).describe('Số lượng sự kiện tối đa'),
          }),
        }
      ),

      // Lấy sự kiện hệ thống gần đây
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');

            // Sử dụng findAll với query để lấy system events
            const query = {
              isSystemEvent: true,
              page: 1,
              limit: _args.limit || 10,
              userId: _args.userId,
            };

            const result = await this.todoCommentService.findAll(tenantId, query);
            const systemEvents = result.items || [];

            return `Tìm thấy ${systemEvents.length} sự kiện hệ thống gần đây:\n${JSON.stringify(systemEvents, null, 2)}`;
          } catch (error) {
            return `Lấy sự kiện hệ thống thất bại: ${error.message}`;
          }
        },
        {
          name: 'get_system_events',
          description: 'Lấy sự kiện hệ thống gần đây',
          schema: z.object({
            userId: z.number().optional().describe('ID người dùng (mặc định là người dùng hiện tại)'),
            eventType: z.enum(['status_changed', 'collaborator_changed', 'assignee_changed', 'all']).optional().default('all').describe('Loại sự kiện'),
            limit: z.number().optional().default(10).describe('Số lượng sự kiện tối đa'),
            startDate: z.string().optional().describe('Ngày bắt đầu (YYYY-MM-DD)'),
            endDate: z.string().optional().describe('Ngày kết thúc (YYYY-MM-DD)'),
          }),
        }
      ),

      // Lấy timeline hoạt động của người dùng
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');
            const userId = _args.userId || parseInt(config?.configurable?.['userId'] || '0');

            // Lấy tất cả comment của user (bao gồm cả system events)
            const query = {
              userId: userId,
              page: 1,
              limit: _args.limit || 20,
            };

            const result = await this.todoCommentService.findAll(tenantId, query);
            const activities = result.items || [];

            // Lọc theo loại hoạt động nếu cần
            let filteredActivities = activities;
            if (!_args.includeComments) {
              filteredActivities = filteredActivities.filter(activity => activity.isSystemEvent);
            }
            if (!_args.includeSystemEvents) {
              filteredActivities = filteredActivities.filter(activity => !activity.isSystemEvent);
            }

            return `Timeline hoạt động của người dùng (${filteredActivities.length} hoạt động):\n${JSON.stringify(filteredActivities, null, 2)}`;
          } catch (error) {
            return `Lấy timeline hoạt động thất bại: ${error.message}`;
          }
        },
        {
          name: 'get_user_activity_timeline',
          description: 'Lấy timeline hoạt động của người dùng',
          schema: z.object({
            userId: z.number().optional().describe('ID người dùng (mặc định là người dùng hiện tại)'),
            limit: z.number().optional().default(20).describe('Số lượng hoạt động tối đa'),
            startDate: z.string().optional().describe('Ngày bắt đầu (YYYY-MM-DD)'),
            endDate: z.string().optional().describe('Ngày kết thúc (YYYY-MM-DD)'),
            includeComments: z.boolean().optional().default(true).describe('Bao gồm bình luận'),
            includeSystemEvents: z.boolean().optional().default(true).describe('Bao gồm sự kiện hệ thống'),
          }),
        }
      ),
    ];
  }
}
