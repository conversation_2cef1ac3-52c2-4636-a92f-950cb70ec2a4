import { Injectable } from '@nestjs/common';
import { AttendanceService } from '@modules/hrm/attendance-management/services/attendance.service';
import { EmployeeService } from '@modules/hrm/employees/services/employee.service';
import { tool, ToolRunnableConfig } from '@langchain/core/tools';
import { z } from 'zod';

/**
 * Attendance Tools Provider
 * Cung cấp các tools liên quan đến chấm công
 */
@Injectable()
export class AttendanceToolsProvider {
  constructor(
    private readonly attendanceService: AttendanceService,
    private readonly employeeService: EmployeeService,
  ) {}

  /**
   * Lấy tất cả attendance tools
   */
  getTools() {
    return [
      // Chấm công vào làm
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');
            const userId = parseInt(config?.configurable?.['userId'] || '0');

            const attendance = await this.attendanceService.checkIn(tenantId, _args.employeeId || userId, {
              location: _args.location,
              notes: _args.note,
            }, userId);
            return `Chấm công vào làm thành công lúc ${new Date(attendance.checkInTime || Date.now()).toLocaleTimeString()}`;
          } catch (error) {
            return `Chấm công vào làm thất bại: ${error.message}`;
          }
        },
        {
          name: 'check_in',
          description: 'Chấm công vào làm',
          schema: z.object({
            employeeId: z.number().optional().describe('ID nhân viên (mặc định là người dùng hiện tại)'),
            location: z.string().optional().describe('Vị trí chấm công'),
            note: z.string().optional().describe('Ghi chú'),
          }),
        }
      ),

      // Chấm công ra về
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');
            const userId = parseInt(config?.configurable?.['userId'] || '0');

            const attendance = await this.attendanceService.checkOut(tenantId, _args.employeeId || userId, {
              location: _args.location,
              notes: _args.note,
            }, userId);
            return `Chấm công ra về thành công lúc ${new Date(attendance.checkOutTime || Date.now()).toLocaleTimeString()}`;
          } catch (error) {
            return `Chấm công ra về thất bại: ${error.message}`;
          }
        },
        {
          name: 'check_out',
          description: 'Chấm công ra về',
          schema: z.object({
            employeeId: z.number().optional().describe('ID nhân viên (mặc định là người dùng hiện tại)'),
            location: z.string().optional().describe('Vị trí chấm công'),
            note: z.string().optional().describe('Ghi chú'),
          }),
        }
      ),

      // Lấy thông tin chấm công hôm nay
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');
            const userId = parseInt(config?.configurable?.['userId'] || '0');
            const employeeId = _args.employeeId || userId;

            const today = new Date().toISOString().split('T')[0];
            const attendance = await this.attendanceService.getAttendanceStats(tenantId, employeeId, new Date(today), new Date(today));

            if (!attendance) {
              return 'Chưa có thông tin chấm công hôm nay';
            }

            return `Thông tin chấm công hôm nay:\n${JSON.stringify(attendance, null, 2)}`;
          } catch (error) {
            return `Lấy thông tin chấm công thất bại: ${error.message}`;
          }
        },
        {
          name: 'get_attendance_today',
          description: 'Lấy thông tin chấm công hôm nay',
          schema: z.object({
            employeeId: z.number().optional().describe('ID nhân viên (mặc định là người dùng hiện tại)'),
          }),
        }
      ),

      // Lấy báo cáo chấm công
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');
            const userId = parseInt(config?.configurable?.['userId'] || '0');

            const report = await this.attendanceService.getAttendanceStats(
              tenantId,
              _args.employeeId || userId,
              new Date(_args.startDate),
              new Date(_args.endDate)
            );
            return `Báo cáo chấm công từ ${_args.startDate} đến ${_args.endDate}:\n${JSON.stringify(report, null, 2)}`;
          } catch (error) {
            return `Tạo báo cáo chấm công thất bại: ${error.message}`;
          }
        },
        {
          name: 'get_attendance_report',
          description: 'Lấy báo cáo chấm công',
          schema: z.object({
            employeeId: z.number().optional().describe('ID nhân viên (mặc định là người dùng hiện tại)'),
            startDate: z.string().describe('Ngày bắt đầu (YYYY-MM-DD)'),
            endDate: z.string().describe('Ngày kết thúc (YYYY-MM-DD)'),
            departmentId: z.number().optional().describe('ID phòng ban (để lấy báo cáo toàn phòng ban)'),
          }),
        }
      ),

      // Lấy danh sách nhân viên đi muộn hôm nay
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');
            const today = new Date().toISOString().split('T')[0];

            const lateEmployees = await this.employeeService.getLateEmployees(tenantId, new Date(today).getTime(), {
              departmentId: _args.departmentId,
              limit: _args.limit,
            });

            return `Tìm thấy ${lateEmployees.length} nhân viên đi muộn hôm nay:\n${JSON.stringify(lateEmployees, null, 2)}`;
          } catch (error) {
            return `Lấy danh sách nhân viên đi muộn thất bại: ${error.message}`;
          }
        },
        {
          name: 'get_late_employees_today',
          description: 'Lấy danh sách nhân viên đi muộn hôm nay',
          schema: z.object({
            departmentId: z.number().optional().describe('ID phòng ban'),
            limit: z.number().optional().default(10).describe('Số lượng kết quả tối đa'),
          }),
        }
      ),
    ];
  }
}
