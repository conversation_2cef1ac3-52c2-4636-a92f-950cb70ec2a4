import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Tag } from '../entities/tag.entity';
import { PaginatedResult } from '@/common/response/api-response-dto';

/**
 * Repository cho entity Tag
 */
@Injectable()
export class TagRepository {
  constructor(
    @InjectRepository(Tag)
    private readonly repository: Repository<Tag>,
  ) {}

  /**
   * Tạo tag mới
   * @param tenantId ID tenant (required for tenant isolation)
   * @param data Dữ liệu tag
   * @returns Tag đã tạo
   */
  async create(tenantId: string, data: Partial<Tag>): Promise<Tag> {
    const tag = this.repository.create({ ...data, tenantId });
    return this.repository.save(tag);
  }

  /**
   * Tìm tag theo ID
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID của tag
   * @returns Tag hoặc null nếu không tìm thấy
   */
  async findById(tenantId: string, id: number): Promise<Tag | null> {
    return this.repository.findOne({
      where: { id, tenantId },
    });
  }

  /**
   * Tìm tag theo tên
   * @param tenantId ID tenant (required for tenant isolation)
   * @param name Tên tag
   * @returns Tag hoặc null nếu không tìm thấy
   */
  async findByName(tenantId: string, name: string): Promise<Tag | null> {
    return this.repository.findOne({
      where: { name, tenantId },
    });
  }

  /**
   * Lấy danh sách tag có phân trang
   * @param tenantId ID tenant (required for tenant isolation)
   * @param page Số trang
   * @param limit Số lượng item trên mỗi trang
   * @param search Từ khóa tìm kiếm
   * @returns Danh sách tag có phân trang
   */
  async findAll(
    tenantId: string,
    page: number = 1,
    limit: number = 10,
    search?: string,
  ): Promise<PaginatedResult<Tag>> {
    const queryBuilder = this.repository.createQueryBuilder('tag');

    queryBuilder.where('tag.tenantId = :tenantId', { tenantId });

    if (search) {
      queryBuilder.andWhere('tag.name LIKE :search', { search: `%${search}%` });
    }

    queryBuilder.orderBy('tag.id', 'DESC');

    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    const [items, totalItems] = await queryBuilder.getManyAndCount();

    const totalPages = Math.ceil(totalItems / limit);

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages,
        currentPage: page,
      },
    };
  }

  /**
   * Lấy tất cả tag không phân trang
   * @param tenantId ID tenant (required for tenant isolation)
   * @param search Từ khóa tìm kiếm
   * @returns Danh sách tất cả tag
   */
  async findAllWithoutPagination(
    tenantId: string,
    search?: string,
  ): Promise<Tag[]> {
    const queryBuilder = this.repository.createQueryBuilder('tag');

    queryBuilder.where('tag.tenantId = :tenantId', { tenantId });

    if (search) {
      queryBuilder.andWhere('tag.name LIKE :search', { search: `%${search}%` });
    }

    queryBuilder.orderBy('tag.name', 'ASC'); // Sắp xếp theo tên cho dropdown

    return queryBuilder.getMany();
  }

  /**
   * Cập nhật tag
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID của tag
   * @param data Dữ liệu cập nhật
   * @returns Tag đã cập nhật hoặc null nếu không tìm thấy
   */
  async update(tenantId: string, id: number, data: Partial<Tag>): Promise<Tag | null> {
    const tag = await this.findById(tenantId, id);
    if (!tag) {
      return null;
    }

    Object.assign(tag, data);
    return this.repository.save(tag);
  }

  /**
   * Xóa tag
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID của tag
   * @returns true nếu xóa thành công
   */
  async delete(tenantId: string, id: number): Promise<boolean> {
    const result = await this.repository.delete({ id, tenantId });
    return (
      result.affected !== null &&
      result.affected !== undefined &&
      result.affected > 0
    );
  }

  /**
   * Xóa nhiều tag
   * @param tenantId ID tenant (required for tenant isolation)
   * @param ids Danh sách ID các tag cần xóa
   * @returns Số lượng tag đã xóa thành công
   */
  async bulkDelete(tenantId: string, ids: number[]): Promise<number> {
    const result = await this.repository
      .createQueryBuilder()
      .delete()
      .from(Tag)
      .where('id IN (:...ids)', { ids })
      .andWhere('tenantId = :tenantId', { tenantId })
      .execute();
    return result.affected || 0;
  }

  /**
   * Kiểm tra tag có tồn tại không
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID của tag
   * @returns true nếu tag tồn tại
   */
  async exists(tenantId: string, id: number): Promise<boolean> {
    const count = await this.repository.count({
      where: { id, tenantId },
    });
    return count > 0;
  }
}
