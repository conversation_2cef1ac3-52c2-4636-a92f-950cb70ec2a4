# Manual Migration Steps for Todo Tags Fix

## Vấn đề
Lỗi foreign key constraint: `todo_tags_labels_id_fkey` vì bảng `todo_tags` đang tham chiếu đến bảng `labels` không tồn tại.

## Giải pháp đã thực hiện trong code:
1. ✅ Đổi `labelsId` thành `tagId` trong entity `TodoTag`
2. ✅ Cập nhật tất cả DTO, Service, Repository
3. ✅ Sửa query join trong `TodoRepository`
4. ✅ Cập nhật chat tools và documentation

## Cần thực hiện trong database:

### Bước 1: Kiểm tra cấu trúc hiện tại
```sql
-- <PERSON>ể<PERSON> tra cột trong bảng todo_tags
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'todo_tags';

-- Kiể<PERSON> tra foreign key constraints
SELECT constraint_name, table_name 
FROM information_schema.table_constraints 
WHERE table_name = 'todo_tags' 
AND constraint_type = 'FOREIGN KEY';
```

### Bước 2: Chạy migration SQL
Chạy file: `src/modules/todolists/migrations/fix-todo-tags-foreign-key.sql`

Hoặc chạy từng bước:

```sql
-- 1. Xóa foreign key constraint cũ
ALTER TABLE todo_tags DROP CONSTRAINT IF EXISTS todo_tags_labels_id_fkey;

-- 2. Đổi tên cột
ALTER TABLE todo_tags RENAME COLUMN labels_id TO tag_id;

-- 3. Thêm foreign key constraint mới
ALTER TABLE todo_tags 
ADD CONSTRAINT todo_tags_tag_id_fkey 
FOREIGN KEY (tag_id) REFERENCES tags(id) ON DELETE CASCADE;

-- 4. Thêm comment
COMMENT ON COLUMN todo_tags.tag_id IS 'ID của tag từ bảng tags';

-- 5. Tạo index
CREATE INDEX IF NOT EXISTS idx_todo_tags_tag_id ON todo_tags(tag_id);
CREATE INDEX IF NOT EXISTS idx_todo_tags_todo_tag ON todo_tags(todo_id, tag_id);
```

### Bước 3: Kiểm tra kết quả
```sql
-- Kiểm tra cột đã được đổi tên
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'todo_tags' 
AND column_name = 'tag_id';

-- Kiểm tra foreign key constraint mới
SELECT constraint_name, table_name 
FROM information_schema.table_constraints 
WHERE constraint_name = 'todo_tags_tag_id_fkey';
```

## Sau khi migration:
1. ✅ Code đã được cập nhật hoàn toàn
2. ⏳ Cần chạy migration SQL trong database
3. ⏳ Test lại chức năng thêm tag vào todo

## Test sau migration:
```bash
# Build lại project
npm run build

# Chạy server
npm run start:dev

# Test API thêm tag vào todo
POST /api/v1/todos/{todoId}/tags
{
  "tagId": 1
}
```
