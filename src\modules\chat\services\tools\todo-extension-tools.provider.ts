import { Injectable } from '@nestjs/common';
import { TodoCommentService, TodoAttachmentService, TodoCollaboratorService, TodoTagService } from '@modules/todolists';
import { tool, ToolRunnableConfig } from '@langchain/core/tools';
import { z } from 'zod';

/**
 * Todo Extension Tools Provider
 * Cung cấp các tools mở rộng cho todo (comments, attachments, collaborators, tags)
 */
@Injectable()
export class TodoExtensionToolsProvider {
  constructor(
    private readonly todoCommentService: TodoCommentService,
    private readonly todoAttachmentService: TodoAttachmentService,
    private readonly todoCollaboratorService: TodoCollaboratorService,
    private readonly todoTagService: TodoTagService,
  ) {}

  /**
   * Lấy tất cả todo extension tools
   */
  getTools() {
    return [
      // Todo Comment Tools
      // Thêm bình luận vào công việc
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');
            const userId = parseInt(config?.configurable?.['userId'] || '0');

            const createCommentDto = {
              todoId: _args.todoId,
              contentHtml: _args.content,
              parentId: _args.parentId,
              createdBy: userId,
            };

            const comment = await this.todoCommentService.createComment(tenantId, userId, createCommentDto);
            return `Bình luận đã được thêm thành công với ID: ${comment.id}`;
          } catch (error) {
            return `Thêm bình luận thất bại: ${error.message}`;
          }
        },
        {
          name: 'add_todo_comment',
          description: 'Thêm bình luận vào công việc',
          schema: z.object({
            todoId: z.number().describe('ID công việc'),
            content: z.string().nonempty().describe('Nội dung bình luận'),
            parentId: z.number().optional().describe('ID bình luận cha (nếu là reply)'),
          }),
        }
      ),

      // Lấy danh sách bình luận
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');

            const comments = await this.todoCommentService.findByTodoId(tenantId, _args.todoId);

            return `Tìm thấy ${comments.length} bình luận cho công việc:\n${JSON.stringify(comments, null, 2)}`;
          } catch (error) {
            return `Lấy danh sách bình luận thất bại: ${error.message}`;
          }
        },
        {
          name: 'get_todo_comments',
          description: 'Lấy danh sách bình luận của công việc',
          schema: z.object({
            todoId: z.number().describe('ID công việc'),
            includeReplies: z.boolean().optional().default(true).describe('Bao gồm các reply'),
          }),
        }
      ),

      // Todo Collaborator Tools
      // Thêm cộng tác viên
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');
            const currentUserId = parseInt(config?.configurable?.['userId'] || '0');

            const addCollaboratorDto = {
              todoId: _args.todoId,
              userId: _args.userId,
              role: _args.role,
              addedBy: currentUserId,
            };

            await this.todoCollaboratorService.addCollaborator(tenantId, currentUserId, addCollaboratorDto);
            return `Cộng tác viên đã được thêm vào công việc thành công`;
          } catch (error) {
            return `Thêm cộng tác viên thất bại: ${error.message}`;
          }
        },
        {
          name: 'add_todo_collaborator',
          description: 'Thêm cộng tác viên vào công việc',
          schema: z.object({
            todoId: z.number().describe('ID công việc'),
            userId: z.number().describe('ID người dùng'),
            role: z.enum(['viewer', 'editor', 'manager']).optional().default('editor').describe('Vai trò cộng tác'),
          }),
        }
      ),

      // Lấy danh sách cộng tác viên
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');

            const collaborators = await this.todoCollaboratorService.findByTodoId(tenantId, _args.todoId);

            return `Tìm thấy ${collaborators.length} cộng tác viên cho công việc:\n${JSON.stringify(collaborators, null, 2)}`;
          } catch (error) {
            return `Lấy danh sách cộng tác viên thất bại: ${error.message}`;
          }
        },
        {
          name: 'get_todo_collaborators',
          description: 'Lấy danh sách cộng tác viên của công việc',
          schema: z.object({
            todoId: z.number().describe('ID công việc'),
          }),
        }
      ),

      // Xóa cộng tác viên
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');
            const currentUserId = parseInt(config?.configurable?.['userId'] || '0');

            await this.todoCollaboratorService.removeCollaborator(tenantId, _args.todoId, _args.userId, currentUserId);
            return `Cộng tác viên đã được xóa khỏi công việc thành công`;
          } catch (error) {
            return `Xóa cộng tác viên thất bại: ${error.message}`;
          }
        },
        {
          name: 'remove_todo_collaborator',
          description: 'Xóa cộng tác viên khỏi công việc',
          schema: z.object({
            todoId: z.number().describe('ID công việc'),
            userId: z.number().describe('ID người dùng'),
          }),
        }
      ),

      // Todo Attachment Tools
      // Lấy danh sách tệp đính kèm
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');

            const attachments = await this.todoAttachmentService.findByTodoId(tenantId, _args.todoId);

            return `Tìm thấy ${attachments.length} tệp đính kèm cho công việc:\n${JSON.stringify(attachments, null, 2)}`;
          } catch (error) {
            return `Lấy danh sách tệp đính kèm thất bại: ${error.message}`;
          }
        },
        {
          name: 'get_todo_attachments',
          description: 'Lấy danh sách tệp đính kèm của công việc',
          schema: z.object({
            todoId: z.number().describe('ID công việc'),
          }),
        }
      ),

      // Tạo URL upload cho tệp đính kèm
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');
            const userId = parseInt(config?.configurable?.['userId'] || '0');

            const createUploadUrlDto = {
              todoId: _args.todoId,
              fileName: _args.fileName,
              fileSize: _args.fileSize,
              mimeType: _args.mimeType,
            };

            const uploadUrl = await this.todoAttachmentService.createUploadUrl(tenantId, userId, createUploadUrlDto);
            return `URL upload đã được tạo thành công:\n${JSON.stringify(uploadUrl, null, 2)}`;
          } catch (error) {
            return `Tạo URL upload thất bại: ${error.message}`;
          }
        },
        {
          name: 'create_upload_url',
          description: 'Tạo URL upload cho tệp đính kèm',
          schema: z.object({
            todoId: z.number().describe('ID công việc'),
            fileName: z.string().nonempty().describe('Tên tệp'),
            fileSize: z.number().describe('Kích thước tệp (bytes)'),
            mimeType: z.string().describe('Loại MIME của tệp'),
          }),
        }
      ),

      // Xác nhận upload tệp đính kèm
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');
            const userId = parseInt(config?.configurable?.['userId'] || '0');

            const confirmUploadDto = {
              todoId: _args.todoId,
              s3Key: _args.s3Key,
              fileName: _args.fileName,
              contentType: _args.contentType,
              size: _args.size,
              uploadId: _args.uploadId,
            };

            const attachment = await this.todoAttachmentService.confirmUpload(tenantId, userId, confirmUploadDto);
            return `Tệp đính kèm "${attachment.filename}" đã được xác nhận thành công với ID: ${attachment.id}`;
          } catch (error) {
            return `Xác nhận upload thất bại: ${error.message}`;
          }
        },
        {
          name: 'confirm_upload',
          description: 'Xác nhận upload tệp đính kèm',
          schema: z.object({
            todoId: z.number().describe('ID công việc'),
            s3Key: z.string().describe('S3 key của tệp đã upload'),
            fileName: z.string().describe('Tên tệp'),
            contentType: z.string().optional().describe('Loại MIME của tệp'),
            size: z.number().optional().describe('Kích thước tệp (bytes)'),
            uploadId: z.string().optional().describe('ID upload từ createUploadUrl'),
          }),
        }
      ),

      // Xóa tệp đính kèm
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');
            const userId = parseInt(config?.configurable?.['userId'] || '0');

            await this.todoAttachmentService.removeAttachment(tenantId, userId, _args.attachmentId);
            return `Tệp đính kèm đã được xóa thành công`;
          } catch (error) {
            return `Xóa tệp đính kèm thất bại: ${error.message}`;
          }
        },
        {
          name: 'delete_todo_attachment',
          description: 'Xóa tệp đính kèm',
          schema: z.object({
            attachmentId: z.number().describe('ID tệp đính kèm'),
          }),
        }
      ),

      // Todo Tag Tools
      // Thêm tag vào công việc
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');
            const userId = parseInt(config?.configurable?.['userId'] || '0');

            const createTodoTagDto = {
              tagId: _args.tagId,
            };

            const todoTag = await this.todoTagService.addTagToTodo(
              tenantId,
              _args.todoId,
              userId,
              createTodoTagDto
            );

            return `Tag đã được thêm vào công việc thành công:\n${JSON.stringify(todoTag, null, 2)}`;
          } catch (error) {
            return `Thêm tag thất bại: ${error.message}`;
          }
        },
        {
          name: 'add_todo_tag',
          description: 'Thêm tag vào công việc',
          schema: z.object({
            todoId: z.number().describe('ID công việc'),
            tagId: z.number().describe('ID tag cần thêm'),
          }),
        }
      ),

      // Lấy danh sách tag của công việc
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');

            const tags = await this.todoTagService.findTagsByTodoId(tenantId, _args.todoId);

            return `Tìm thấy ${tags.length} tag cho công việc:\n${JSON.stringify(tags, null, 2)}`;
          } catch (error) {
            return `Lấy danh sách tag thất bại: ${error.message}`;
          }
        },
        {
          name: 'get_todo_tags',
          description: 'Lấy danh sách tag của công việc',
          schema: z.object({
            todoId: z.number().describe('ID công việc'),
          }),
        }
      ),

      // Xóa tag khỏi công việc
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');
            const userId = parseInt(config?.configurable?.['userId'] || '0');

            const result = await this.todoTagService.removeTagFromTodo(
              tenantId,
              _args.todoId,
              _args.tagId,
              userId
            );

            return result
              ? `Tag đã được xóa khỏi công việc thành công`
              : `Xóa tag khỏi công việc thất bại`;
          } catch (error) {
            return `Xóa tag thất bại: ${error.message}`;
          }
        },
        {
          name: 'remove_todo_tag',
          description: 'Xóa tag khỏi công việc',
          schema: z.object({
            todoId: z.number().describe('ID công việc'),
            tagId: z.number().describe('ID tag cần xóa'),
          }),
        }
      ),
    ];
  }
}
