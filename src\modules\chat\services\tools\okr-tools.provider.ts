import { Injectable } from '@nestjs/common';
import { KeyResultService, ObjectiveService, OkrCycleService } from '@modules/okrs/services';
import { tool, ToolRunnableConfig } from '@langchain/core/tools';
import { z } from 'zod';
import { ObjectiveType } from '@modules/okrs/enum/objective-type.enum';

/**
 * OKR Tools Provider
 * Cung cấp các tools liên quan đến OKR (Objectives and Key Results)
 */
@Injectable()
export class OkrToolsProvider {
  constructor(
    private readonly keyResultService: KeyResultService,
    private readonly objectiveService: ObjectiveService,
    private readonly okrCycleService: OkrCycleService,
  ) {}

  /**
   * L<PERSON>y tất cả OKR tools
   */
  getTools() {
    return [
      // Tạo Objective mới
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');
            const userId = parseInt(config?.configurable?.['userId'] || '0');

            const createObjectiveDto = {
              title: _args.title,
              description: _args.description,
              cycleId: _args.cycleId,
              ownerId: _args.ownerId || userId,
              parentId: _args.parentId,
              weight: _args.weight,
              type: ObjectiveType.INDIVIDUAL,
              startDate: new Date().toISOString(),
              endDate: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString(),
              createdBy: userId,
            };

            const objective = await this.objectiveService.create(tenantId, userId, createObjectiveDto);
            return `Objective "${objective.title}" đã được tạo thành công với ID: ${objective.id}`;
          } catch (error) {
            return `Tạo Objective thất bại: ${error.message}`;
          }
        },
        {
          name: 'create_objective',
          description: 'Tạo Objective mới',
          schema: z.object({
            title: z.string().nonempty().describe('Tiêu đề Objective'),
            description: z.string().optional().describe('Mô tả chi tiết'),
            cycleId: z.number().describe('ID chu kỳ OKR'),
            ownerId: z.number().optional().describe('ID người sở hữu (mặc định là người tạo)'),
            parentId: z.number().optional().describe('ID Objective cha (nếu có)'),
            weight: z.number().optional().default(1).describe('Trọng số (1-10)'),
          }),
        }
      ),

      // Tạo Key Result mới
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');
            const userId = parseInt(config?.configurable?.['userId'] || '0');

            const createKeyResultDto = {
              objectiveId: _args.objectiveId,
              title: _args.title,
              description: _args.description,
              targetValue: _args.targetValue,
              currentValue: _args.currentValue,
              unit: _args.unit,
              weight: _args.weight,
              createdBy: userId,
            };

            const keyResult = await this.keyResultService.create(tenantId, createKeyResultDto);
            return `Key Result "${keyResult.title}" đã được tạo thành công với ID: ${keyResult.id}`;
          } catch (error) {
            return `Tạo Key Result thất bại: ${error.message}`;
          }
        },
        {
          name: 'create_key_result',
          description: 'Tạo Key Result mới',
          schema: z.object({
            objectiveId: z.number().describe('ID Objective liên quan'),
            title: z.string().nonempty().describe('Tiêu đề Key Result'),
            description: z.string().optional().describe('Mô tả chi tiết'),
            targetValue: z.number().describe('Giá trị mục tiêu'),
            currentValue: z.number().optional().default(0).describe('Giá trị hiện tại'),
            unit: z.string().optional().describe('Đơn vị đo lường'),
            weight: z.number().optional().default(1).describe('Trọng số (1-10)'),
          }),
        }
      ),

      // Cập nhật tiến độ Key Result
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');
            const userId = parseInt(config?.configurable?.['userId'] || '0');

            const updateDto = {
              currentValue: _args.currentValue,
              note: _args.note,
              updatedBy: userId,
            };

            const keyResult = await this.keyResultService.update(tenantId, _args.keyResultId, updateDto);
            return `Tiến độ Key Result "${keyResult.title}" đã được cập nhật thành công`;
          } catch (error) {
            return `Cập nhật tiến độ Key Result thất bại: ${error.message}`;
          }
        },
        {
          name: 'update_key_result_progress',
          description: 'Cập nhật tiến độ Key Result',
          schema: z.object({
            keyResultId: z.number().describe('ID Key Result'),
            currentValue: z.number().describe('Giá trị hiện tại mới'),
            note: z.string().optional().describe('Ghi chú về cập nhật'),
          }),
        }
      ),
    ];
  }
}
