import { Module, Global } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AuthModule } from '@/modules/auth/auth.module';

// Entities
import { OkrCycle } from './entities/okr-cycle.entity';
import { Objective } from './entities/objective.entity';
import { KeyResult } from './entities/key-result.entity';
import { KeyResultUpdate } from './entities/key-result-update.entity';
import { KeyResultSupport } from './entities/key-result-supports';

// Repositories
import { OkrCycleRepository } from './repositories/okr-cycle.repository';
import { ObjectiveRepository } from './repositories/objective.repository';
import { KeyResultRepository } from './repositories/key-result.repository';
import { KeyResultUpdateRepository } from './repositories/key-result-update.repository';
import { KeyResultSupportRepository } from './repositories/key-result-support.repository';

// Services
import { OkrCycleService } from './services/okr-cycle.service';
import { ObjectiveService } from './services/objective.service';
import { KeyResultService } from './services/key-result.service';
import { KeyResultSupportService } from './services/key-result-support.service';

// Controllers
import { OkrCycleController } from './controllers/okr-cycle.controller';
import { ObjectiveController } from './controllers/objective.controller';
import { KeyResultController } from './controllers/key-result.controller';
import { KeyResultSupportController } from './controllers/key-result-support.controller';

/**
 * Module for OKRs (Objectives and Key Results)
 */
@Global()
@Module({
  imports: [
    AuthModule,
    TypeOrmModule.forFeature([
      OkrCycle,
      Objective,
      KeyResult,
      KeyResultUpdate,
      KeyResultSupport,
    ]),
  ],
  providers: [
    // Repositories
    OkrCycleRepository,
    ObjectiveRepository,
    KeyResultRepository,
    KeyResultUpdateRepository,
    KeyResultSupportRepository,

    // Services
    OkrCycleService,
    ObjectiveService,
    KeyResultService,
    KeyResultSupportService,
  ],
  controllers: [
    OkrCycleController,
    ObjectiveController,
    KeyResultController,
    KeyResultSupportController,
  ],
  exports: [
    // Repositories
    OkrCycleRepository,
    ObjectiveRepository,
    KeyResultRepository,
    KeyResultUpdateRepository,
    KeyResultSupportRepository,

    // Services
    OkrCycleService,
    ObjectiveService,
    KeyResultService,
    KeyResultSupportService,
  ],
})
export class OkrsModule {}
