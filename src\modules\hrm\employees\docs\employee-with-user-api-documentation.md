# API Documentation - Employee With User Management

## Overview
API để quản lý nhân viên kèm tài khoản ng<PERSON><PERSON><PERSON> dùng, bao gồm vi<PERSON><PERSON> tạo, liên kết và lấy danh sách nhân viên đã có tài khoản user.

## Base URL
```
/api/hrm/employees/with-user
```

## Authentication
Tất cả các endpoint đều yêu cầu JWT token trong header:
```
Authorization: Bearer <JWT_TOKEN>
```

## Endpoints

### 1. GET / - Lấy danh sách nhân viên đã có tài khoản user

#### Description
Lấy danh sách tất cả nhân viên đã có tài khoản user với phân trang và các tùy chọn lọc.

#### Request
```http
GET /api/hrm/employees/with-user?page=1&limit=10&search=Nguyễn&status=ACTIVE
```

#### Query Parameters
| Parameter | Type | Required | Description | Example |
|-----------|------|----------|-------------|---------|
| page | number | No | Số trang (mặc định: 1) | 1 |
| limit | number | No | Số lượng item trên mỗi trang (mặc định: 10) | 20 |
| search | string | No | Tìm kiếm theo tên, email, vị trí | "Nguyễn" |
| sortBy | string | No | Trường để sắp xếp | "createdAt" |
| sortDirection | string | No | Hướng sắp xếp (ASC/DESC) | "DESC" |
| status | string | No | Lọc theo trạng thái | "ACTIVE" |
| departmentId | number | No | Lọc theo phòng ban | 1 |
| userType | string | No | Lọc theo loại người dùng | "EMPLOYEE" |

#### Response Example
```json
{
  "success": true,
  "message": "Lấy danh sách nhân viên đã có tài khoản user thành công",
  "data": {
    "items": [
      {
        "id": 1,
        "email": "<EMAIL>",
        "fullName": "Nguyễn Văn A",
        "employeeId": 1,
        "departmentId": 1,
        "departmentName": "Phòng Kỹ thuật",
        "status": "ACTIVE",
        "position": "Lập trình viên",
        "phoneNumber": "0123456789",
        "address": "123 Đường ABC, Quận 1, TP.HCM",
        "dateOfBirth": 631152000000,
        "gender": "Nam",
        "userType": "EMPLOYEE",
        "createdAt": 1640995200000,
        "employeeCode": "REDAI001",
        "employeeName": "Nguyễn Văn A"
      }
    ],
    "meta": {
      "totalItems": 50,
      "itemCount": 10,
      "itemsPerPage": 10,
      "totalPages": 5,
      "currentPage": 1
    }
  }
}
```

### 2. POST / - Tạo nhân viên mới kèm tài khoản người dùng

#### Description
Tạo mới một nhân viên cùng với tài khoản người dùng tương ứng.

#### Request Body
```json
{
  "employeeInfo": {
    "employeeName": "Nguyễn Văn B",
    "dateOfBirth": "1990-01-01",
    "gender": "male",
    "jobTitle": "Lập trình viên",
    "departmentId": 1
  },
  "userInfo": {
    "email": "<EMAIL>",
    "password": "password123",
    "fullName": "Nguyễn Văn B"
  },
  "departmentId": 1
}
```

### 3. POST /create-user - Tạo tài khoản người dùng cho nhân viên

#### Description
Tạo tài khoản người dùng cho một nhân viên đã tồn tại.

#### Request Body
```json
{
  "employeeId": 1,
  "email": "<EMAIL>",
  "autoGeneratePassword": true
}
```

### 4. POST /link-employee - Gắn nhân viên với tài khoản người dùng

#### Description
Liên kết một nhân viên hiện có với một tài khoản người dùng hiện có.

#### Request Body
```json
{
  "employeeId": 1,
  "userId": 2
}
```

## Features
- ✅ **Pagination**: Hỗ trợ phân trang với meta information
- ✅ **Search**: Tìm kiếm theo tên, email, vị trí
- ✅ **Filtering**: Lọc theo trạng thái, phòng ban, loại người dùng
- ✅ **Sorting**: Sắp xếp theo bất kỳ trường nào
- ✅ **Related Data**: Tự động load tên phòng ban và thông tin nhân viên
- ✅ **Tenant Isolation**: Đảm bảo dữ liệu được phân tách theo tenant

## Error Codes
- `EMAIL_EXISTS`: Email đã được sử dụng
- `EMPLOYEE_NOT_FOUND`: Không tìm thấy nhân viên
- `USER_NOT_FOUND`: Không tìm thấy người dùng
- `USER_FETCH_FAILED`: Lỗi khi lấy danh sách/chi tiết người dùng

## Usage Examples

### Lấy danh sách nhân viên có tài khoản user với phân trang
```bash
curl -X GET "http://localhost:3000/api/hrm/employees/with-user?page=1&limit=20&status=ACTIVE" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Tìm kiếm nhân viên theo tên
```bash
curl -X GET "http://localhost:3000/api/hrm/employees/with-user?search=Nguyễn&page=1&limit=10" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Lọc nhân viên theo phòng ban
```bash
curl -X GET "http://localhost:3000/api/hrm/employees/with-user?departmentId=1&sortBy=fullName&sortDirection=ASC" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## Performance Notes
- API sử dụng database indexing cho các trường thường xuyên query
- Lazy loading cho related data (department, employee)
- Caching có thể được implement ở service layer nếu cần
- Tenant isolation được đảm bảo ở mọi query
