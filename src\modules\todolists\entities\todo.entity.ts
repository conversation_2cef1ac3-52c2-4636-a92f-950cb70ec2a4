import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';
import { TodoStatus } from '../enum/todo-status.enum';
import { TodoPriority } from '../enum/todo-priority.enum';

/**
 * Entity đại diện cho một công việc cần làm
 */
@Entity('todos')
export class Todo {
  /**
   * Unique identifier for the todo
   */
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * Todo title
   */
  @Column({ type: 'varchar', length: 255, nullable: false })
  title: string;

  /**
   * Detailed description of the todo
   */
  @Column({ type: 'text', nullable: true })
  description: string | null;

  /**
   * ID of the user assigned to the todo
   */
  @Column({ name: 'assignee_id', type: 'integer', nullable: true })
  assigneeId: number;

  /**
   * Trạng thái của Todo (pending, in_progress, completed, approved, rejected).
   * Mặc định là 'pending', có thể null.
   */
  @Column({
    type: 'varchar',
    length: 50,
    default: TodoStatus.PENDING,
    nullable: true,
  })
  status: TodoStatus | null;

  /**
   * <PERSON><PERSON><PERSON> độ ưu tiên của Todo (low, medium, high, urgent).
   * Mặc định là 'medium', có thể null.
   */
  @Column({
    type: 'varchar',
    length: 20,
    default: TodoPriority.MEDIUM,
    nullable: true,
  })
  priority: TodoPriority | null;

  /**
   * Expected stars for the todo (1-5)
   */
  @Column({
    name: 'expected_stars',
    type: 'integer',
    default: 3,
    nullable: true,
  })
  expectedStars: number | null;

  /**
   * Actual stars awarded after completion
   */
  @Column({ name: 'awarded_stars', type: 'integer', nullable: true })
  awardedStars: number | null;

  /**
   * ID of the user who created the todo
   */
  @Column({ name: 'created_by', type: 'integer', nullable: false })
  createdBy: number;

  /**
   * Creation timestamp (in milliseconds)
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: true })
  createdAt: number | null;

  /**
   * Last update timestamp (in milliseconds)
   */
  @Column({ name: 'updated_at', type: 'bigint', nullable: true })
  updatedAt: number | null;

  /**
   * Completion timestamp (in milliseconds)
   */
  @Column({ name: 'completed_at', type: 'bigint', nullable: true })
  completedAt: number | null;

  /**
   * Deadline timestamp (in milliseconds)
   */
  @Column({ name: 'deadline', type: 'bigint', nullable: true })
  deadline: number | null;

  /**
   * ID of the project/category the todo belongs to
   */
  @Column({ name: 'category_id', type: 'integer', nullable: true })
  categoryId: number | null;

  /**
   * ID of the parent todo (if this is a subtask)
   */
  @Column({ name: 'parent_id', type: 'integer', nullable: true })
  parentId: number | null;

  /**
   * ID of the company/organization that owns this record
   */
  @Column({ name: 'tenant_id', type: 'integer', nullable: true })
  tenantId: number = 0;
}
