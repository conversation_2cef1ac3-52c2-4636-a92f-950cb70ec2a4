import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@/modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@/modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import {
  ApiResponseDto,
  PaginatedResult,
} from '@/common/response/api-response-dto';
import { SWAGGER_API_TAG } from '@/common/swagger/swagger.tags';
import { TagService } from '../services/tag.service';
import { CreateTagDto } from '../dto/tag/create-tag.dto';
import { UpdateTagDto } from '../dto/tag/update-tag.dto';
import { TagQueryDto } from '../dto/tag/tag-query.dto';
import { TagListQueryDto } from '../dto/tag/tag-list-query.dto';
import { TagResponseDto } from '../dto/tag/tag-response.dto';
import { BulkDeleteTagDto } from '../dto/tag/bulk-delete-tag.dto';
import { BulkDeleteTagResponseDto } from '../dto/tag/bulk-delete-tag-response.dto';

/**
 * Controller xử lý các API liên quan đến tag
 */
@ApiTags(SWAGGER_API_TAG.TODOLISTS)
@ApiExtraModels(
  ApiResponseDto,
  TagResponseDto,
  BulkDeleteTagResponseDto,
)
@Controller('/todolists/tags')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class TagController {
  constructor(private readonly tagService: TagService) {}

  /**
   * Tạo tag mới
   */
  @Post()
  @ApiOperation({ summary: 'Tạo tag mới' })
  @ApiResponse({
    status: 201,
    description: 'Tag đã được tạo thành công',
    schema: ApiResponseDto.getSchema(TagResponseDto),
  })
  async createTag(
    @CurrentUser() user: JwtPayload,
    @Body() createTagDto: CreateTagDto,
  ): Promise<ApiResponseDto<TagResponseDto>> {
    const tag = await this.tagService.createTag(String(user.tenantId), createTagDto);
    return ApiResponseDto.created(tag);
  }

  /**
   * Lấy danh sách tag có phân trang
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách tag có phân trang' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách tag có phân trang',
    schema: ApiResponseDto.getPaginatedSchema(TagResponseDto),
  })
  async findAllTags(
    @CurrentUser() user: JwtPayload,
    @Query() query: TagQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<TagResponseDto>>> {
    const paginatedTags = await this.tagService.findAllTags(
      String(user.tenantId),
      query,
    );
    return ApiResponseDto.paginated(paginatedTags);
  }

  /**
   * Lấy tất cả tag không phân trang (dùng cho dropdown, select)
   */
  @Get('all')
  @ApiOperation({ summary: 'Lấy tất cả tag không phân trang' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách tất cả tag',
    schema: ApiResponseDto.getArraySchema(TagResponseDto),
  })
  async getAllTags(
    @CurrentUser() user: JwtPayload,
    @Query() query: TagListQueryDto,
  ): Promise<ApiResponseDto<TagResponseDto[]>> {
    const tags = await this.tagService.getAllTagsWithoutPagination(
      String(user.tenantId),
      query.search,
    );
    return ApiResponseDto.success(tags);
  }

  /**
   * Lấy thông tin tag theo ID
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy thông tin tag theo ID' })
  @ApiParam({ name: 'id', description: 'ID của tag', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Thông tin tag',
    schema: ApiResponseDto.getSchema(TagResponseDto),
  })
  async findTagById(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<TagResponseDto>> {
    const tag = await this.tagService.findTagById(String(user.tenantId), id);
    return ApiResponseDto.success(tag);
  }

  /**
   * Cập nhật tag
   */
  @Patch(':id')
  @ApiOperation({ summary: 'Cập nhật tag' })
  @ApiParam({ name: 'id', description: 'ID của tag', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Tag đã được cập nhật thành công',
    schema: ApiResponseDto.getSchema(TagResponseDto),
  })
  async updateTag(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) id: number,
    @Body() updateTagDto: UpdateTagDto,
  ): Promise<ApiResponseDto<TagResponseDto>> {
    const tag = await this.tagService.updateTag(String(user.tenantId), id, updateTagDto);
    return ApiResponseDto.success(tag);
  }

  /**
   * Xóa tag
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa tag' })
  @ApiParam({ name: 'id', description: 'ID của tag', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Tag đã được xóa thành công',
    schema: ApiResponseDto.getSchema('boolean'),
  })
  async deleteTag(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<boolean>> {
    const result = await this.tagService.deleteTag(String(user.tenantId), id);
    return ApiResponseDto.deleted(result);
  }

  /**
   * Xóa nhiều tag
   */
  @Delete()
  @ApiOperation({ summary: 'Xóa nhiều tag' })
  @ApiResponse({
    status: 200,
    description: 'Kết quả xóa nhiều tag',
    schema: ApiResponseDto.getSchema(BulkDeleteTagResponseDto),
  })
  async bulkDeleteTags(
    @CurrentUser() user: JwtPayload,
    @Body() bulkDeleteTagDto: BulkDeleteTagDto,
  ): Promise<ApiResponseDto<BulkDeleteTagResponseDto>> {
    const result = await this.tagService.bulkDeleteTags(
      String(user.tenantId),
      bulkDeleteTagDto.ids,
    );
    return ApiResponseDto.success(result);
  }
}
