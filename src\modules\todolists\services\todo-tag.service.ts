import { Injectable, Logger } from '@nestjs/common';
import { TodoTagRepository } from '../repositories/todo-tag.repository';
import { TodoRepository } from '../repositories/todo.repository';
import { TodoTag } from '../entities/todo-tag.entity';
import { AppException } from '@/common/exceptions/app.exception';
import { TODOLISTS_ERROR_CODES } from '../errors/todolists-error.code';
import { CreateTodoTagDto } from '../dto/todo-tag/create-todo-tag.dto';

/**
 * Service xử lý logic nghiệp vụ cho liên kết giữa todo và tag
 */
@Injectable()
export class TodoTagService {
  private readonly logger = new Logger(TodoTagService.name);

  constructor(
    private readonly todoTagRepository: TodoTagRepository,
    private readonly todoRepository: TodoRepository,
  ) {}

  /**
   * Thêm tag vào todo
   * @param tenantId ID tenant (required for tenant isolation)
   * @param todoId ID của todo
   * @param userId ID người dùng thực hiện hành động
   * @param createTodoTagDto Thông tin tag cần thêm
   * @returns Liên kết đã tạo
   */
  async addTagToTodo(
    tenantId: number,
    todoId: number,
    userId: number,
    createTodoTagDto: CreateTodoTagDto,
  ): Promise<TodoTag> {
    try {
      // Kiểm tra todo tồn tại
      const todo = await this.todoRepository.findById(tenantId, todoId);
      if (!todo) {
        throw new AppException(
          TODOLISTS_ERROR_CODES.TODO_NOT_FOUND,
          `Không tìm thấy công việc với ID ${todoId}`,
        );
      }

      // Kiểm tra tag đã tồn tại chưa
      const existingTag = await this.todoTagRepository.findByTodoIdAndTagId(
        tenantId,
        todoId,
        createTodoTagDto.tagId,
      );
      if (existingTag) {
        throw new AppException(
          TODOLISTS_ERROR_CODES.TAG_ALREADY_EXISTS,
          'Tag đã được gắn với công việc này',
        );
      }

      // Tạo liên kết mới
      return await this.todoTagRepository.create(tenantId, {
        todoId,
        tagId: createTodoTagDto.tagId,
        createdAt: Date.now(),
      });
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi thêm tag vào todo: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        TODOLISTS_ERROR_CODES.TAG_CREATION_FAILED,
        `Thêm tag vào todo thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách tag của todo
   * @param tenantId ID tenant (required for tenant isolation)
   * @param todoId ID của todo
   * @returns Danh sách tag
   */
  async findTagsByTodoId(tenantId: number, todoId: number): Promise<TodoTag[]> {
    try {
      // Kiểm tra todo tồn tại
      const todo = await this.todoRepository.findById(tenantId, todoId);
      if (!todo) {
        throw new AppException(
          TODOLISTS_ERROR_CODES.TODO_NOT_FOUND,
          `Không tìm thấy công việc với ID ${todoId}`,
        );
      }

      // Lấy danh sách tag
      return await this.todoTagRepository.findByTodoId(tenantId, todoId);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi lấy danh sách tag của todo: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        TODOLISTS_ERROR_CODES.TAG_NOT_FOUND,
        `Lấy danh sách tag của todo thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Xóa tag khỏi todo
   * @param tenantId ID tenant (required for tenant isolation)
   * @param todoId ID của todo
   * @param tagId ID của tag
   * @param userId ID người dùng thực hiện hành động
   * @returns true nếu xóa thành công
   */
  async removeTagFromTodo(
    tenantId: number,
    todoId: number,
    tagId: number,
    userId: number,
  ): Promise<boolean> {
    try {
      // Kiểm tra todo tồn tại
      const todo = await this.todoRepository.findById(tenantId, todoId);
      if (!todo) {
        throw new AppException(
          TODOLISTS_ERROR_CODES.TODO_NOT_FOUND,
          `Không tìm thấy công việc với ID ${todoId}`,
        );
      }

      // Kiểm tra quyền xóa tag (chỉ người được giao hoặc người tạo mới có quyền xóa tag)
      if (todo.assigneeId !== userId && todo.createdBy !== userId) {
        throw new AppException(
          TODOLISTS_ERROR_CODES.NOT_TODO_ASSIGNEE,
          'Bạn không có quyền xóa tag khỏi công việc này',
        );
      }

      // Kiểm tra tag tồn tại
      const tag = await this.todoTagRepository.findById(tenantId, tagId);
      if (!tag || tag.todoId !== todoId) {
        throw new AppException(
          TODOLISTS_ERROR_CODES.TAG_NOT_FOUND,
          'Không tìm thấy tag',
        );
      }

      // Xóa tag
      const result = await this.todoTagRepository.delete(tenantId, tagId);
      if (!result) {
        throw new AppException(
          TODOLISTS_ERROR_CODES.TAG_DELETE_FAILED,
          'Xóa tag thất bại',
        );
      }
      return true;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi xóa tag khỏi todo: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        TODOLISTS_ERROR_CODES.TAG_DELETE_FAILED,
        `Xóa tag khỏi todo thất bại: ${error.message}`,
      );
    }
  }
}
