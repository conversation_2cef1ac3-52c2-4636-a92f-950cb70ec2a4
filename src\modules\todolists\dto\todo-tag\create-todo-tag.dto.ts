import { ApiProperty } from '@nestjs/swagger';
import { IsInt, IsNotEmpty, Min } from 'class-validator';

/**
 * DTO cho tạo liên kết giữa todo và tag
 */
export class CreateTodoTagDto {
  /**
   * ID của tag
   * @example 1
   */
  @ApiProperty({
    description: 'ID của tag',
    example: 1,
    required: true,
  })
  @IsNotEmpty({ message: 'ID tag không được để trống' })
  @IsInt({ message: 'ID tag phải là số nguyên' })
  @Min(1, { message: 'ID tag phải lớn hơn 0' })
  tagId: number;
}
