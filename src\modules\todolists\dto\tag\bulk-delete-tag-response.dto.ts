import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho kết quả xóa nhiều tag
 */
export class BulkDeleteTagResponseDto {
  /**
   * Tổng số tag được yêu cầu xóa
   */
  @ApiProperty({
    description: 'Tổng số tag được yêu cầu xóa',
    example: 5,
    type: Number,
  })
  totalRequested: number;

  /**
   * Số tag đã xóa thành công
   */
  @ApiProperty({
    description: 'Số tag đã xóa thành công',
    example: 3,
    type: Number,
  })
  successCount: number;

  /**
   * <PERSON><PERSON> tag không thể xóa
   */
  @ApiProperty({
    description: 'Số tag không thể xóa',
    example: 2,
    type: Number,
  })
  failureCount: number;

  /**
   * Danh sách ID các tag đã xóa thành công
   */
  @ApiProperty({
    description: 'Danh sách ID các tag đã xóa thành công',
    example: [1, 3, 5],
    type: [Number],
  })
  successIds: number[];

  /**
   * <PERSON>h sách ID các tag không thể xóa
   */
  @ApiProperty({
    description: 'Danh sách ID các tag không thể xóa',
    example: [2, 4],
    type: [Number],
  })
  failureIds: number[];
}
