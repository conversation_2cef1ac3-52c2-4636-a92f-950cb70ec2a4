import { Injectable } from '@nestjs/common';
import { StatisticsService } from '@modules/todolists/services/statistics.service';
import { tool, ToolRunnableConfig } from '@langchain/core/tools';
import { z } from 'zod';

/**
 * Statistics Tools Provider
 * Cung cấp các tools liên quan đến thống kê
 */
@Injectable()
export class StatisticsToolsProvider {
  constructor(private readonly statisticsService: StatisticsService) {}

  /**
   * Lấy tất cả statistics tools
   */
  getTools() {
    return [
      // Thống kê công việc
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');
            const userId = _args.userId || parseInt(config?.configurable?.['userId'] || '0');

            const stats = await this.statisticsService.getTodoStatistics(tenantId, userId, {
              dateRange: _args.dateRange,
              teamId: _args.teamId,
              includeCompleted: _args.includeCompleted,
            });

            return `Thống kê công việc ${_args.dateRange}:\n${JSON.stringify(stats, null, 2)}`;
          } catch (error) {
            return `Lấy thống kê công việc thất bại: ${error.message}`;
          }
        },
        {
          name: 'get_todo_statistics',
          description: 'Lấy thống kê công việc của người dùng hoặc team',
          schema: z.object({
            userId: z.number().optional().describe('ID người dùng (mặc định là người dùng hiện tại)'),
            teamId: z.number().optional().describe('ID team'),
            dateRange: z.enum(['today', 'week', 'month', 'quarter', 'year']).optional().default('week').describe('Khoảng thời gian thống kê'),
            includeCompleted: z.boolean().optional().default(true).describe('Bao gồm công việc đã hoàn thành'),
          }),
        }
      ),

      // Thống kê team
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');

            const teamStats = await this.statisticsService.getTeamStatistics(tenantId, _args.teamId, {
              dateRange: _args.dateRange,
              includeIndividual: _args.includeIndividual,
            });

            return `Thống kê team:\n${JSON.stringify(teamStats, null, 2)}`;
          } catch (error) {
            return `Lấy thống kê team thất bại: ${error.message}`;
          }
        },
        {
          name: 'get_team_statistics',
          description: 'Lấy thống kê hiệu suất team/phòng ban',
          schema: z.object({
            teamId: z.number().describe('ID team/phòng ban'),
            dateRange: z.enum(['week', 'month', 'quarter']).optional().default('month').describe('Khoảng thời gian thống kê'),
            includeIndividual: z.boolean().optional().default(false).describe('Bao gồm thống kê từng cá nhân'),
          }),
        }
      ),

      // Thống kê nhân viên
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');

            const stats = await this.statisticsService.getEmployeeStats(tenantId, _args.employeeId);

            return `Thống kê nhân viên:\n${JSON.stringify(stats, null, 2)}`;
          } catch (error) {
            return `Lấy thống kê nhân viên thất bại: ${error.message}`;
          }
        },
        {
          name: 'get_employee_stats',
          description: 'Lấy thống kê hiệu suất nhân viên',
          schema: z.object({
            employeeId: z.number().describe('ID nhân viên'),
            dateRange: z.enum(['week', 'month', 'quarter']).optional().default('month').describe('Khoảng thời gian thống kê'),
          }),
        }
      ),

      // Thống kê dự án
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');

            const projectStats = await this.statisticsService.getProjectPerformance(tenantId, _args.projectId);

            return `Thống kê dự án:\n${JSON.stringify(projectStats, null, 2)}`;
          } catch (error) {
            return `Lấy thống kê dự án thất bại: ${error.message}`;
          }
        },
        {
          name: 'get_project_statistics',
          description: 'Lấy thống kê hiệu suất dự án',
          schema: z.object({
            projectId: z.number().describe('ID dự án'),
            includeMembers: z.boolean().optional().default(false).describe('Bao gồm thống kê từng thành viên'),
          }),
        }
      ),
    ];
  }
}
