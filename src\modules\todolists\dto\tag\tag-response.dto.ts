import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho phản hồi thông tin tag
 */
export class TagResponseDto {
  /**
   * ID của tag
   * @example 1
   */
  @ApiProperty({
    description: 'ID của tag',
    example: 1,
  })
  id: number;

  /**
   * Tên của tag
   * @example "Urgent"
   */
  @ApiProperty({
    description: 'Tên của tag',
    example: 'Urgent',
  })
  name: string;

  /**
   * ID của tenant/công ty sở hữu tag này
   * @example "123"
   */
  @ApiProperty({
    description: 'ID của tenant/công ty sở hữu tag này',
    example: '123',
    nullable: true,
  })
  tenantId: string | null;
}
