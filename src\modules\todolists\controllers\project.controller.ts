import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@/modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@/modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import {
  ApiResponseDto,
  PaginatedResult,
} from '@/common/response/api-response-dto';
import { SWAGGER_API_TAG } from '@/common/swagger/swagger.tags';
import { ProjectService } from '../services/project.service';
import { CreateProjectDto } from '../dto/project/create-project.dto';
import { UpdateProjectDto } from '../dto/project/update-project.dto';
import { ProjectQueryDto } from '../dto/project/project-query.dto';
import { ProjectResponseDto } from '../dto/project/project-response.dto';
import { CreateProjectMemberDto } from '../dto/project-member/create-project-member.dto';
import { UpdateProjectMemberDto } from '../dto/project-member/update-project-member.dto';
import { ProjectMemberResponseDto } from '../dto/project-member/project-member-response.dto';
import { QueryDto } from '@/common/dto/query.dto';
import { RequirePermissionEnum } from '@/modules/auth/decorators/require-module-action.decorator';
import { Permission } from '@/modules/auth/enum/permission.enum';
import { PermissionsGuard } from '@/modules/auth/guards/permissions.guard';

import { ProjectMember } from '../entities/project-members.entity';
import { AppException } from '@/common/exceptions/app.exception';
import { TODOLISTS_ERROR_CODES } from '../errors/todolists-error.code';

/**
 * Controller xử lý các API liên quan đến dự án
 */
@ApiTags(SWAGGER_API_TAG.TODOLISTS)
@ApiExtraModels(ApiResponseDto, ProjectResponseDto, ProjectMemberResponseDto)
@Controller('/projects')
@UseGuards(JwtUserGuard, PermissionsGuard)
@ApiBearerAuth('JWT-auth')
export class ProjectController {
  constructor(private readonly projectService: ProjectService) {}

  /**
   * Chuyển đổi từ Project entity sang ProjectResponseDto
   */
  @RequirePermissionEnum(Permission.PROJECT_CREATE)
  @Post()
  @ApiOperation({ summary: 'Tạo dự án mới' })
  @ApiResponse({
    status: 201,
    description: 'Dự án đã được tạo thành công',
    schema: ApiResponseDto.getSchema(ProjectResponseDto),
  })
  async createProject(
    @CurrentUser() user: JwtPayload,
    @Body() createProjectDto: CreateProjectDto,
  ): Promise<ApiResponseDto<ProjectResponseDto>> {
    const project = await this.projectService.createProject(
      Number(user.tenantId),
      user.id,
      createProjectDto,
    );
    return ApiResponseDto.created(project);
  }

  /**
   * Lấy danh sách dự án
   */
  @Get()
  @RequirePermissionEnum(Permission.PROJECT_VIEW_LIST)
  @ApiOperation({ summary: 'Lấy danh sách dự án' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách dự án',
    schema: ApiResponseDto.getPaginatedSchema(ProjectResponseDto),
  })
  async findAll(
    @CurrentUser() user: JwtPayload,
    @Query() query: ProjectQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<ProjectResponseDto>>> {
    const paginatedProjects = await this.projectService.findAllProjects(
      Number(user.tenantId),
      query,
    );
    return ApiResponseDto.paginated(paginatedProjects);
  }

  /**
   * Lấy chi tiết dự án
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy chi tiết dự án' })
  @ApiParam({ name: 'id', description: 'ID dự án', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Chi tiết dự án',
    schema: ApiResponseDto.getSchema(ProjectResponseDto),
  })
  async findProjectById(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<ProjectResponseDto>> {
    const project = await this.projectService.findProjectById(
      Number(user.tenantId),
      id,
    );
    return ApiResponseDto.success(project);
  }

  /**
   * Cập nhật dự án
   */
  @Patch(':id')
  @ApiOperation({ summary: 'Cập nhật dự án' })
  @ApiParam({ name: 'id', description: 'ID dự án', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Dự án đã được cập nhật thành công',
    schema: ApiResponseDto.getSchema(ProjectResponseDto),
  })
  async updateProject(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: JwtPayload,
    @Body() updateProjectDto: UpdateProjectDto,
  ): Promise<ApiResponseDto<ProjectResponseDto>> {
    const project = await this.projectService.updateProject(
      Number(user.tenantId),
      id,
      user.id,
      updateProjectDto,
    );
    if (!project) {
      throw new Error(`Không tìm thấy dự án với ID ${id}`);
    }
    return ApiResponseDto.success(project);
  }

  /**
   * Xóa dự án
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa dự án' })
  @ApiParam({ name: 'id', description: 'ID dự án', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Dự án đã được xóa thành công',
    schema: ApiResponseDto.getSchema(Boolean),
  })
  async deleteProject(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<boolean>> {
    await this.projectService.deleteProject(Number(user.tenantId), id, user.id);
    return ApiResponseDto.deleted();
  }

  /**
   * Thêm thành viên vào dự án
   */
  @Post(':id/members')
  @ApiOperation({ summary: 'Thêm thành viên vào dự án' })
  @ApiParam({ name: 'id', description: 'ID dự án', type: 'number' })
  @ApiResponse({
    status: 201,
    description: 'Thành viên đã được thêm vào dự án thành công',
    schema: ApiResponseDto.getSchema(ProjectMemberResponseDto),
  })
  async addProjectMember(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: JwtPayload,
    @Body() createProjectMemberDto: CreateProjectMemberDto,
  ): Promise<ApiResponseDto<ProjectMemberResponseDto>> {
    const member = await this.projectService.addProjectMember(
      Number(user.tenantId),
      id,
      user.id,
      createProjectMemberDto,
    );
    return ApiResponseDto.created(member);
  }

  /**
   * Lấy danh sách thành viên dự án
   */
  @Get(':id/members')
  @ApiOperation({ summary: 'Lấy danh sách thành viên dự án' })
  @ApiParam({ name: 'id', description: 'ID dự án', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách thành viên dự án',
    schema: ApiResponseDto.getPaginatedSchema(ProjectMemberResponseDto),
  })
  async findProjectMembers(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: JwtPayload,
    @Query() query: QueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<ProjectMemberResponseDto>>> {
    const paginatedMembers = await this.projectService.findProjectMembers(
      Number(user.tenantId),
      id,
      user.id,
      query,
    );
    return ApiResponseDto.paginated(paginatedMembers);
  }

  /**
   * Cập nhật vai trò thành viên dự án
   */
  @Patch(':id/members/:memberId')
  @ApiOperation({ summary: 'Cập nhật vai trò thành viên dự án' })
  @ApiParam({ name: 'id', description: 'ID dự án', type: 'number' })
  @ApiParam({ name: 'memberId', description: 'ID thành viên', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Vai trò thành viên đã được cập nhật thành công',
    schema: ApiResponseDto.getSchema(ProjectMemberResponseDto),
  })
  async updateProjectMember(
    @Param('id', ParseIntPipe) id: number,
    @Param('memberId', ParseIntPipe) memberId: number,
    @CurrentUser() user: JwtPayload,
    @Body() updateProjectMemberDto: UpdateProjectMemberDto,
  ): Promise<ApiResponseDto<ProjectMemberResponseDto>> {
    const member = await this.projectService.updateProjectMember(
      Number(user.tenantId),
      id,
      memberId,
      user.id,
      updateProjectMemberDto,
    );
    if (!member) {
      throw new Error(`Không tìm thấy thành viên dự án với ID ${memberId}`);
    }
    return ApiResponseDto.success(member);
  }

  /**
   * Xóa thành viên khỏi dự án
   */
  @Delete(':id/members/:memberId')
  @ApiOperation({ summary: 'Xóa thành viên khỏi dự án' })
  @ApiParam({ name: 'id', description: 'ID dự án', type: 'number' })
  @ApiParam({ name: 'memberId', description: 'ID thành viên', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Thành viên đã được xóa khỏi dự án thành công',
    schema: ApiResponseDto.getSchema(Boolean),
  })
  async removeProjectMember(
    @Param('id', ParseIntPipe) id: number,
    @Param('memberId', ParseIntPipe) memberId: number,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<boolean>> {
    await this.projectService.removeProjectMember(
      Number(user.tenantId),
      id,
      memberId,
      user.id,
    );
    return ApiResponseDto.deleted();
  }
}
