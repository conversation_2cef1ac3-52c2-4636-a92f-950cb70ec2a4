import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { Request } from 'express';

/**
 * Custom decorator để xử lý array parameters từ query string
 * Hỗ trợ cả format tagIds=1,2,3 và tagIds[]=1&tagIds[]=2
 */
export const QueryArray = createParamDecorator(
  (propertyName: string, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest<Request>();
    const query = request.query;
    
    // Lấy giá trị từ property name thông thường
    let value = query[propertyName];
    
    // N<PERSON>u không có, thử lấy từ array syntax
    if (!value) {
      value = query[`${propertyName}[]`];
    }
    
    if (!value) {
      return undefined;
    }
    
    // Xử lý array
    if (Array.isArray(value)) {
      return value.map(item => parseInt(String(item))).filter(id => !isNaN(id));
    }
    
    // Xử lý string với comma-separated values
    if (typeof value === 'string') {
      if (value.includes(',')) {
        return value.split(',').map(item => parseInt(item.trim())).filter(id => !isNaN(id));
      }
      // Single value
      const parsed = parseInt(value);
      return !isNaN(parsed) ? [parsed] : undefined;
    }
    
    // Xử lý number
    if (typeof value === 'number') {
      return [value];
    }
    
    return undefined;
  },
);
