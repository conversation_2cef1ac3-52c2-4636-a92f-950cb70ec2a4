-- Migration để sửa lỗi foreign key constraint trong bảng todo_tags
-- Đ<PERSON><PERSON> từ labels_id sang tag_id và tham chiếu đến bảng tags

-- Bước 1: Xóa foreign key constraint cũ (nếu tồn tại)
DO $$ 
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'todo_tags_labels_id_fkey' 
        AND table_name = 'todo_tags'
    ) THEN
        ALTER TABLE todo_tags DROP CONSTRAINT todo_tags_labels_id_fkey;
    END IF;
END $$;

-- Bước 2: Đ<PERSON><PERSON> tên cột từ labels_id sang tag_id
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'todo_tags' 
        AND column_name = 'labels_id'
    ) THEN
        ALTER TABLE todo_tags RENAME COLUMN labels_id TO tag_id;
    END IF;
END $$;

-- Bước 3: Thêm foreign key constraint mới tham chiếu đến bảng tags
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'todo_tags_tag_id_fkey' 
        AND table_name = 'todo_tags'
    ) THEN
        ALTER TABLE todo_tags 
        ADD CONSTRAINT todo_tags_tag_id_fkey 
        FOREIGN KEY (tag_id) REFERENCES tags(id) ON DELETE CASCADE;
    END IF;
END $$;

-- Bước 4: Thêm comment cho cột mới
COMMENT ON COLUMN todo_tags.tag_id IS 'ID của tag từ bảng tags';

-- Bước 5: Tạo index cho hiệu suất truy vấn
CREATE INDEX IF NOT EXISTS idx_todo_tags_tag_id ON todo_tags(tag_id);
CREATE INDEX IF NOT EXISTS idx_todo_tags_todo_tag ON todo_tags(todo_id, tag_id);
