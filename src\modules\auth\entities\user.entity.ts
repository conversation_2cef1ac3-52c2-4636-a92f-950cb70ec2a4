import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';
import { UserStatus } from '../enum/user-status.enum';

/**
 * Entity representing users in the system
 */
@Entity('users')
export class User {
  /**
   * Unique identifier for the user
   */
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * User's email address
   */
  @Column({ type: 'varchar', length: 255, nullable: false })
  email: string;

  /**
   * User's encrypted password
   */
  @Column({ type: 'varchar', length: 255, nullable: false })
  password: string;

  /**
   * User account status
   */
  @Column({ type: 'enum', enum: UserStatus, nullable: true })
  status: UserStatus | null;

  /**
   * Creation timestamp (in milliseconds)
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: true })
  createdAt: number | null;

  /**
   * ID of the company/organization that owns this record
   */
  @Column({ name: 'tenant_id', type: 'integer', nullable: true })
  tenantId: number | null;
}
