import { ApiProperty } from '@nestjs/swagger';
import { IsInt, IsNotEmpty, Min } from 'class-validator';

/**
 * DTO cho thiết lập deadline của công việc
 */
export class SetDeadlineDto {
  /**
   * Thời gian deadline (timestamp trong milliseconds)
   * @example 1703980800000
   */
  @ApiProperty({
    description: 'Thời gian deadline (timestamp trong milliseconds)',
    example: 1703980800000,
    required: true,
  })
  @IsNotEmpty({ message: 'Deadline không được để trống' })
  @IsInt({ message: 'Deadline phải là số nguyên (timestamp)' })
  @Min(Date.now(), { message: 'Deadline phải là thời gian trong tương lai' })
  deadline: number;
}
