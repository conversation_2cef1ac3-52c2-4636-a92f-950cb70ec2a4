const { Client } = require('pg');
const fs = require('fs');
const path = require('path');

// Database configuration
const dbConfig = {
  host: 'localhost',
  port: 5432,
  database: 'backend_ai_erp',
  user: 'root',
  password: 'root123',
};

async function runMigration() {
  const client = new Client(dbConfig);
  
  try {
    console.log('Connecting to database...');
    await client.connect();
    console.log('Connected successfully!');

    // Read migration file
    const migrationPath = path.join(__dirname, 'src/modules/todolists/migrations/fix-todo-tags-foreign-key.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

    console.log('Running migration...');
    console.log('Migration SQL:');
    console.log(migrationSQL);
    console.log('\n--- Executing migration ---\n');

    // Execute migration
    await client.query(migrationSQL);
    
    console.log('Migration completed successfully!');

    // Verify the changes
    console.log('\n--- Verifying changes ---\n');
    
    // Check if column was renamed
    const columnCheck = await client.query(`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_name = 'todo_tags' 
      AND column_name IN ('tag_id', 'labels_id')
      ORDER BY column_name;
    `);
    
    console.log('Columns in todo_tags table:');
    console.table(columnCheck.rows);

    // Check foreign key constraints
    const constraintCheck = await client.query(`
      SELECT constraint_name, table_name, column_name, foreign_table_name, foreign_column_name
      FROM information_schema.key_column_usage kcu
      JOIN information_schema.referential_constraints rc ON kcu.constraint_name = rc.constraint_name
      JOIN information_schema.key_column_usage fkcu ON rc.unique_constraint_name = fkcu.constraint_name
      WHERE kcu.table_name = 'todo_tags'
      AND kcu.column_name = 'tag_id';
    `);
    
    console.log('\nForeign key constraints:');
    console.table(constraintCheck.rows);

  } catch (error) {
    console.error('Migration failed:', error.message);
    console.error('Full error:', error);
  } finally {
    await client.end();
    console.log('Database connection closed.');
  }
}

// Run the migration
runMigration();
