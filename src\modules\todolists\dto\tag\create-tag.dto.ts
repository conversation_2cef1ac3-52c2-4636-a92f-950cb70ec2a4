import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, MaxLength } from 'class-validator';

/**
 * DTO cho tạo tag mới
 */
export class CreateTagDto {
  /**
   * Tên của tag
   * @example "Urgent"
   */
  @ApiProperty({
    description: 'Tên của tag',
    example: 'Urgent',
    required: true,
  })
  @IsNotEmpty({ message: 'Tên tag không được để trống' })
  @IsString({ message: 'Tên tag phải là chuỗi' })
  @MaxLength(255, { message: 'Tên tag không được vượt quá 255 ký tự' })
  name: string;
}
