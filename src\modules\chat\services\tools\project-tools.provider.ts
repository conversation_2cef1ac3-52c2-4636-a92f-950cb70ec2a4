import { Injectable } from '@nestjs/common';
import { ProjectService } from '@modules/todolists/services/project.service';
import { tool, ToolRunnableConfig } from '@langchain/core/tools';
import { z } from 'zod';

/**
 * Project Tools Provider
 * Cung cấp các tools liên quan đến quản lý dự án
 */
@Injectable()
export class ProjectToolsProvider {
  constructor(private readonly projectService: ProjectService) {}

  /**
   * Lấy tất cả project tools
   */
  getTools() {
    return [
      // Tạo dự án mới
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');
            const userId = parseInt(config?.configurable?.['userId'] || '0');

            const createProjectDto = {
              title: _args.title,
              description: _args.description,
              ownerId: _args.ownerId || userId,
            };

            const project = await this.projectService.createProject(tenantId, userId, createProjectDto);
            return `Dự án "${project.title}" đã được tạo thành công với ID: ${project.id}`;
          } catch (error) {
            return `Tạo dự án thất bại: ${error.message}`;
          }
        },
        {
          name: 'create_project',
          description: 'Tạo dự án mới',
          schema: z.object({
            title: z.string().nonempty().describe('Tiêu đề dự án'),
            description: z.string().optional().describe('Mô tả dự án'),
            ownerId: z.number().optional().describe('ID người sở hữu dự án (mặc định là người tạo)'),
          }),
        }
      ),

      // Lấy danh sách dự án
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');

            const query = {
              isActive: _args.isActive,
              page: _args.page || 1,
              limit: _args.limit || 10,
              search: _args.search,
            };

            const projects = await this.projectService.findAllProjects(tenantId, query);
            return `Tìm thấy ${projects.items?.length || 0} dự án:\n${JSON.stringify(projects, null, 2)}`;
          } catch (error) {
            return `Lấy danh sách dự án thất bại: ${error.message}`;
          }
        },
        {
          name: 'get_project_list',
          description: 'Lấy danh sách dự án',
          schema: z.object({
            isActive: z.boolean().optional().describe('Lọc theo trạng thái hoạt động'),
            page: z.number().optional().default(1).describe('Số trang'),
            limit: z.number().optional().default(10).describe('Số lượng kết quả tối đa'),
            search: z.string().optional().describe('Từ khóa tìm kiếm'),
          }),
        }
      ),

      // Lấy chi tiết dự án
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');

            const project = await this.projectService.findProjectById(tenantId, _args.projectId);
            return `Chi tiết dự án "${project.title}":\n${JSON.stringify(project, null, 2)}`;
          } catch (error) {
            return `Lấy chi tiết dự án thất bại: ${error.message}`;
          }
        },
        {
          name: 'get_project_details',
          description: 'Lấy chi tiết dự án',
          schema: z.object({
            projectId: z.number().describe('ID dự án'),
            includeMembers: z.boolean().optional().default(false).describe('Bao gồm danh sách thành viên'),
            includeTasks: z.boolean().optional().default(false).describe('Bao gồm danh sách công việc'),
          }),
        }
      ),

      // Cập nhật dự án
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');
            const userId = parseInt(config?.configurable?.['userId'] || '0');

            const updateDto = {
              title: _args.title,
              description: _args.description,
              isActive: _args.isActive,
            };

            const project = await this.projectService.updateProject(tenantId, _args.projectId, userId, updateDto);
            return `Dự án "${project?.title || 'N/A'}" đã được cập nhật thành công`;
          } catch (error) {
            return `Cập nhật dự án thất bại: ${error.message}`;
          }
        },
        {
          name: 'update_project',
          description: 'Cập nhật thông tin dự án',
          schema: z.object({
            projectId: z.number().describe('ID dự án'),
            title: z.string().optional().describe('Tiêu đề dự án mới'),
            description: z.string().optional().describe('Mô tả dự án mới'),
            isActive: z.boolean().optional().describe('Trạng thái hoạt động'),
          }),
        }
      ),

      // Xóa dự án
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');
            const userId = parseInt(config?.configurable?.['userId'] || '0');

            const result = await this.projectService.deleteProject(tenantId, _args.projectId, userId);
            return result ? `Dự án đã được xóa thành công` : `Xóa dự án thất bại`;
          } catch (error) {
            return `Xóa dự án thất bại: ${error.message}`;
          }
        },
        {
          name: 'delete_project',
          description: 'Xóa dự án',
          schema: z.object({
            projectId: z.number().describe('ID dự án'),
          }),
        }
      ),

      // Thêm thành viên vào dự án
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');
            const currentUserId = parseInt(config?.configurable?.['userId'] || '0');

            const createMemberDto = {
              userId: _args.userId,
              role: _args.role as any,
            };

            const member = await this.projectService.addProjectMember(tenantId, _args.projectId, currentUserId, createMemberDto);
            return `Thành viên đã được thêm vào dự án thành công:\n${JSON.stringify(member, null, 2)}`;
          } catch (error) {
            return `Thêm thành viên vào dự án thất bại: ${error.message}`;
          }
        },
        {
          name: 'add_project_member',
          description: 'Thêm thành viên vào dự án',
          schema: z.object({
            projectId: z.number().describe('ID dự án'),
            userId: z.number().describe('ID người dùng'),
            role: z.enum(['admin', 'member', 'viewer']).describe('Vai trò trong dự án'),
          }),
        }
      ),

      // Lấy danh sách thành viên dự án
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');
            const userId = parseInt(config?.configurable?.['userId'] || '0');

            const query = {
              page: _args.page || 1,
              limit: _args.limit || 10,
            };

            const members = await this.projectService.findProjectMembers(tenantId, _args.projectId, userId, query);
            return `Tìm thấy ${members.items?.length || 0} thành viên trong dự án:\n${JSON.stringify(members, null, 2)}`;
          } catch (error) {
            return `Lấy danh sách thành viên dự án thất bại: ${error.message}`;
          }
        },
        {
          name: 'get_project_members',
          description: 'Lấy danh sách thành viên dự án',
          schema: z.object({
            projectId: z.number().describe('ID dự án'),
            page: z.number().optional().default(1).describe('Số trang'),
            limit: z.number().optional().default(10).describe('Số lượng kết quả tối đa'),
          }),
        }
      ),

      // Cập nhật vai trò thành viên
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');
            const userId = parseInt(config?.configurable?.['userId'] || '0');

            const updateMemberDto = {
              role: _args.role as any,
            };

            const member = await this.projectService.updateProjectMember(tenantId, _args.projectId, _args.memberId, userId, updateMemberDto);
            return `Vai trò thành viên đã được cập nhật thành công:\n${JSON.stringify(member, null, 2)}`;
          } catch (error) {
            return `Cập nhật vai trò thành viên thất bại: ${error.message}`;
          }
        },
        {
          name: 'update_project_member',
          description: 'Cập nhật vai trò thành viên dự án',
          schema: z.object({
            projectId: z.number().describe('ID dự án'),
            memberId: z.number().describe('ID thành viên'),
            role: z.enum(['admin', 'member', 'viewer']).describe('Vai trò mới'),
          }),
        }
      ),

      // Xóa thành viên khỏi dự án
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');
            const userId = parseInt(config?.configurable?.['userId'] || '0');

            const result = await this.projectService.removeProjectMember(tenantId, _args.projectId, _args.memberId, userId);
            return result ? `Thành viên đã được xóa khỏi dự án thành công` : `Xóa thành viên khỏi dự án thất bại`;
          } catch (error) {
            return `Xóa thành viên khỏi dự án thất bại: ${error.message}`;
          }
        },
        {
          name: 'remove_project_member',
          description: 'Xóa thành viên khỏi dự án',
          schema: z.object({
            projectId: z.number().describe('ID dự án'),
            memberId: z.number().describe('ID thành viên'),
          }),
        }
      ),


    ];
  }
}
