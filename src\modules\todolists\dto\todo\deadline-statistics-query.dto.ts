import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsInt, IsOptional, IsString, Min } from 'class-validator';
import { Type, Transform } from 'class-transformer';

/**
 * DTO cho query thống kê deadline
 */
export class DeadlineStatisticsQueryDto {
  /**
   * ID của dự án để lọc
   * @example 1
   */
  @ApiProperty({
    description: 'ID của dự án để lọc',
    example: 1,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'ID dự án phải là số nguyên' })
  @Min(1, { message: 'ID dự án phải lớn hơn 0' })
  projectId?: number;

  /**
   * ID của người được giao để lọc
   * @example 2
   */
  @ApiProperty({
    description: 'ID của người được giao để lọc',
    example: 2,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'ID người được giao phải là số nguyên' })
  @Min(1, { message: 'ID người được giao phải lớn hơn 0' })
  assigneeId?: number;

  /**
   * Danh sách tags để lọc (cách nhau bởi dấu phẩy)
   * @example "urgent,important"
   */
  @ApiProperty({
    description: 'Danh sách tags để lọc (cách nhau bởi dấu phẩy)',
    example: 'urgent,important',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tags phải là chuỗi' })
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0);
    }
    return value;
  })
  tags?: string[];

  /**
   * Thời gian bắt đầu để lọc (timestamp trong milliseconds)
   * @example 1703894400000
   */
  @ApiProperty({
    description: 'Thời gian bắt đầu để lọc (timestamp trong milliseconds)',
    example: 1703894400000,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'Thời gian bắt đầu phải là số nguyên (timestamp)' })
  startDate?: number;

  /**
   * Thời gian kết thúc để lọc (timestamp trong milliseconds)
   * @example 1703980800000
   */
  @ApiProperty({
    description: 'Thời gian kết thúc để lọc (timestamp trong milliseconds)',
    example: 1703980800000,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'Thời gian kết thúc phải là số nguyên (timestamp)' })
  endDate?: number;

  /**
   * Số ngày để cảnh báo trước deadline (mặc định 3 ngày)
   * @example 3
   */
  @ApiProperty({
    description: 'Số ngày để cảnh báo trước deadline (mặc định 3 ngày)',
    example: 3,
    required: false,
    default: 3,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'Số ngày cảnh báo phải là số nguyên' })
  @Min(1, { message: 'Số ngày cảnh báo phải lớn hơn 0' })
  warningDays?: number = 3;
}
