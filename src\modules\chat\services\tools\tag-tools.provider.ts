import { Injectable } from '@nestjs/common';
import { TagService } from '@modules/todolists/services/tag.service';
import { tool, ToolRunnableConfig } from '@langchain/core/tools';
import { z } from 'zod';

/**
 * Tag Tools Provider
 * Cung cấp các tools liên quan đến quản lý tag
 */
@Injectable()
export class TagToolsProvider {
  constructor(private readonly tagService: TagService) {}

  /**
   * Lấy tất cả tag tools
   */
  getTools() {
    return [
      // Tạo tag mới
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = config?.configurable?.['tenantId'] || '0';

            const createTagDto = {
              name: _args.name,
              description: _args.description,
              color: _args.color,
            };

            const tag = await this.tagService.createTag(tenantId, createTagDto);
            return `Tag "${tag.name}" đã được tạo thành công với ID: ${tag.id}`;
          } catch (error) {
            return `Tạo tag thất bại: ${error.message}`;
          }
        },
        {
          name: 'create_tag',
          description: 'Tạo tag mới',
          schema: z.object({
            name: z.string().nonempty().describe('Tên tag'),
            description: z.string().optional().describe('Mô tả tag'),
            color: z.string().optional().describe('Màu sắc tag (hex code)'),
          }),
        }
      ),

      // Lấy danh sách tag
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = config?.configurable?.['tenantId'] || '0';

            const query = {
              page: _args.page || 1,
              limit: _args.limit || 20,
              search: _args.search,
            };

            const tags = await this.tagService.findAllTags(tenantId, query);
            return `Tìm thấy ${tags.items?.length || 0} tag:\n${JSON.stringify(tags, null, 2)}`;
          } catch (error) {
            return `Lấy danh sách tag thất bại: ${error.message}`;
          }
        },
        {
          name: 'get_tag_list',
          description: 'Lấy danh sách tag có phân trang',
          schema: z.object({
            page: z.number().optional().default(1).describe('Số trang'),
            limit: z.number().optional().default(20).describe('Số lượng kết quả tối đa'),
            search: z.string().optional().describe('Từ khóa tìm kiếm'),
          }),
        }
      ),

      // Lấy tất cả tag (không phân trang)
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = config?.configurable?.['tenantId'] || '0';

            const tags = await this.tagService.getAllTagsWithoutPagination(tenantId, _args.search);
            return `Tìm thấy ${tags.length} tag:\n${JSON.stringify(tags, null, 2)}`;
          } catch (error) {
            return `Lấy tất cả tag thất bại: ${error.message}`;
          }
        },
        {
          name: 'get_all_tags',
          description: 'Lấy tất cả tag không phân trang (dùng cho dropdown)',
          schema: z.object({
            search: z.string().optional().describe('Từ khóa tìm kiếm'),
          }),
        }
      ),

      // Lấy chi tiết tag
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = config?.configurable?.['tenantId'] || '0';

            const tag = await this.tagService.findTagById(tenantId, _args.tagId);
            return `Chi tiết tag "${tag.name}":\n${JSON.stringify(tag, null, 2)}`;
          } catch (error) {
            return `Lấy chi tiết tag thất bại: ${error.message}`;
          }
        },
        {
          name: 'get_tag_details',
          description: 'Lấy chi tiết tag theo ID',
          schema: z.object({
            tagId: z.number().describe('ID tag'),
          }),
        }
      ),

      // Cập nhật tag
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = config?.configurable?.['tenantId'] || '0';

            const updateTagDto = {
              name: _args.name,
              description: _args.description,
              color: _args.color,
            };

            const tag = await this.tagService.updateTag(tenantId, _args.tagId, updateTagDto);
            return `Tag "${tag.name}" đã được cập nhật thành công`;
          } catch (error) {
            return `Cập nhật tag thất bại: ${error.message}`;
          }
        },
        {
          name: 'update_tag',
          description: 'Cập nhật thông tin tag',
          schema: z.object({
            tagId: z.number().describe('ID tag'),
            name: z.string().optional().describe('Tên tag mới'),
            description: z.string().optional().describe('Mô tả tag mới'),
            color: z.string().optional().describe('Màu sắc tag mới (hex code)'),
          }),
        }
      ),

      // Xóa tag
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = config?.configurable?.['tenantId'] || '0';

            const deleted = await this.tagService.deleteTag(tenantId, _args.tagId);
            return deleted ? 'Tag đã được xóa thành công' : 'Xóa tag thất bại';
          } catch (error) {
            return `Xóa tag thất bại: ${error.message}`;
          }
        },
        {
          name: 'delete_tag',
          description: 'Xóa tag',
          schema: z.object({
            tagId: z.number().describe('ID tag'),
          }),
        }
      ),

      // Xóa nhiều tag
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = config?.configurable?.['tenantId'] || '0';

            const result = await this.tagService.bulkDeleteTags(tenantId, _args.tagIds);
            return `Xóa tag hoàn tất: ${result.successCount}/${result.totalRequested} thành công\n${JSON.stringify(result, null, 2)}`;
          } catch (error) {
            return `Xóa nhiều tag thất bại: ${error.message}`;
          }
        },
        {
          name: 'bulk_delete_tags',
          description: 'Xóa nhiều tag cùng lúc',
          schema: z.object({
            tagIds: z.array(z.number()).describe('Danh sách ID các tag cần xóa'),
          }),
        }
      ),
    ];
  }
}
