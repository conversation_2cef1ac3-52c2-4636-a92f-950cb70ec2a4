import { Injectable } from '@nestjs/common';
import { TodoService } from '@modules/todolists/services/todo.service';
import { EmployeeService } from '@modules/hrm/employees/services/employee.service';
import { StatisticsService } from '@modules/todolists/services/statistics.service';
import { EmailService } from '@modules/email/services';
import { KeyResultService, KeyResultSupportService, ObjectiveService, OkrCycleService } from '@modules/okrs/services';
import {
  ProjectService,
  TodoAttachmentService,
  TodoCollaboratorService,
  TodoCommentService,
  TodoTagService,
  TagService,
  EventService,
} from '@modules/todolists';
import { AttendanceService } from '@modules/hrm/attendance-management/services/attendance.service';
import { DepartmentService } from '@modules/hrm/org-units/services/department.service';
import { DepartmentMembersService } from '@modules/hrm/org-units/services/department-members.service';

// Import tool providers
import {
  EmailToolsProvider,
  TodoToolsProvider,
  EmployeeToolsProvider,
  StatisticsToolsProvider,
  OkrToolsProvider,
  ProjectToolsProvider,
  DepartmentToolsProvider,
  AttendanceToolsProvider,
  TodoExtensionToolsProvider,
  TagToolsProvider,
  EventToolsProvider,
} from './tools';

/**
 * Business Tools Provider cho ERP system
 * Cung cấp tất cả tools liên quan đến business logic
 * Mỗi tool tương ứng với một chức năng cụ thể trong ERP
 */

export interface RunContextShape {
  tenantId: string;
  userId: string;
}

@Injectable()
export class BusinessToolsProvider {
  constructor(
    // Tool providers
    private readonly emailToolsProvider: EmailToolsProvider,
    private readonly todoToolsProvider: TodoToolsProvider,
    private readonly employeeToolsProvider: EmployeeToolsProvider,
    private readonly statisticsToolsProvider: StatisticsToolsProvider,
    private readonly okrToolsProvider: OkrToolsProvider,
    private readonly projectToolsProvider: ProjectToolsProvider,
    private readonly departmentToolsProvider: DepartmentToolsProvider,
    private readonly attendanceToolsProvider: AttendanceToolsProvider,
    private readonly todoExtensionToolsProvider: TodoExtensionToolsProvider,
    private readonly tagToolsProvider: TagToolsProvider,
    private readonly eventToolsProvider: EventToolsProvider,

    // Legacy services for backward compatibility
    private readonly keyResultSupportService: KeyResultSupportService,
  ) {}

  /**
   * Khởi tạo và trả về tất cả business tools đã định nghĩa
   * Đây là entry point để sử dụng các tools trong hệ thống
   */
  getEmailTools() {
    return this.emailToolsProvider.getTools();
  }

  getTodoTools() {
    return this.todoToolsProvider.getTools();
  }

  getEmployeeTools() {
    return this.employeeToolsProvider.getTools();
  }

  getStatisticsTools() {
    return this.statisticsToolsProvider.getTools();
  }

  getOkrTools() {
    return this.okrToolsProvider.getTools();
  }

  getProjectTools() {
    return this.projectToolsProvider.getTools();
  }

  getDepartmentTools() {
    return this.departmentToolsProvider.getTools();
  }

  getAttendanceTools() {
    return this.attendanceToolsProvider.getTools();
  }

  getTodoExtensionTools() {
    return this.todoExtensionToolsProvider.getTools();
  }

  getTagTools() {
    return this.tagToolsProvider.getTools();
  }

  getEventTools() {
    return this.eventToolsProvider.getTools();
  }

  // Legacy method for KeyResultSupport tools
  getKeyResultSupportTools() {
    return []; // TODO: Implement if needed
  }

  /**
   * Lấy tất cả business tools đã định nghĩa
   * Method này sẽ được gọi khi khởi tạo để register tools
   */
  getAllBusinessTools() {
    return [
      ...this.getEmailTools(),
      ...this.getTodoTools(),
      ...this.getEmployeeTools(),
      ...this.getStatisticsTools(),
      ...this.getOkrTools(),
      ...this.getProjectTools(),
      ...this.getDepartmentTools(),
      ...this.getAttendanceTools(),
      ...this.getTodoExtensionTools(),
      ...this.getTagTools(),
      ...this.getEventTools(),
      ...this.getKeyResultSupportTools(),
    ];
  }
}









