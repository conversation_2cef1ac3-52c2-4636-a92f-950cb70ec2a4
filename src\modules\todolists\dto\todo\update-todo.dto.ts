import { ApiProperty } from '@nestjs/swagger';
import {
  IsEnum,
  IsInt,
  IsOptional,
  IsString,
  Max,
  <PERSON>,
  <PERSON>,
} from 'class-validator';
import { TodoPriority } from '../../enum/todo-priority.enum';
import { TodoStatus } from '../../enum/todo-status.enum';

/**
 * DTO cho cập nhật công việc
 */
export class UpdateTodoDto {
  /**
   * Tiêu đề công việc
   * @example "Thiết kế giao diện người dùng (cập nhật)"
   */
  @ApiProperty({
    description: 'Tiêu đề công việc',
    example: 'Thiết kế giao diện người dùng (cập nhật)',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tiêu đề phải là chuỗi' })
  @MaxLength(255, { message: 'Tiêu đề không được vượt quá 255 ký tự' })
  title?: string;

  /**
   * <PERSON><PERSON> tả chi tiết công việc
   * @example "Thiết kế giao diện người dùng cho trang chủ và trang sản phẩm (cập nhật)"
   */
  @ApiProperty({
    description: 'Mô tả chi tiết công việc',
    example:
      'Thiết kế giao diện người dùng cho trang chủ và trang sản phẩm (cập nhật)',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Mô tả phải là chuỗi' })
  description?: string;

  /**
   * ID của người được giao công việc
   * @example 2
   */
  @ApiProperty({
    description: 'ID của người được giao công việc',
    example: 2,
    required: false,
  })
  @IsOptional()
  @IsInt({ message: 'ID người được giao phải là số nguyên' })
  @Min(1, { message: 'ID người được giao phải lớn hơn 0' })
  assigneeId?: number;

  /**
   * Trạng thái công việc
   * @example "in_progress"
   */
  @ApiProperty({
    description: 'Trạng thái công việc',
    enum: TodoStatus,
    example: TodoStatus.IN_PROGRESS,
    required: false,
  })
  @IsOptional()
  @IsEnum(TodoStatus, { message: 'Trạng thái không hợp lệ' })
  status?: TodoStatus;

  /**
   * Mức độ ưu tiên của công việc
   * @example "high"
   */
  @ApiProperty({
    description: 'Mức độ ưu tiên của công việc',
    enum: TodoPriority,
    example: TodoPriority.HIGH,
    required: false,
  })
  @IsOptional()
  @IsEnum(TodoPriority, { message: 'Mức độ ưu tiên không hợp lệ' })
  priority?: TodoPriority;

  /**
   * Số sao kỳ vọng (1-5)
   * @example 4
   */
  @ApiProperty({
    description: 'Số sao kỳ vọng (1-5)',
    example: 4,
    required: false,
  })
  @IsOptional()
  @IsInt({ message: 'Số sao kỳ vọng phải là số nguyên' })
  @Min(1, { message: 'Số sao kỳ vọng phải từ 1 đến 5' })
  @Max(5, { message: 'Số sao kỳ vọng phải từ 1 đến 5' })
  expectedStars?: number;

  /**
   * ID của dự án
   * @example 2
   */
  @ApiProperty({
    description: 'ID của dự án',
    example: 2,
    required: false,
  })
  @IsOptional()
  @IsInt({ message: 'ID dự án phải là số nguyên' })
  @Min(1, { message: 'ID dự án phải lớn hơn 0' })
  categoryId?: number;

  /**
   * Thời gian deadline (timestamp trong milliseconds)
   * @example 1703980800000
   */
  @ApiProperty({
    description: 'Thời gian deadline (timestamp trong milliseconds)',
    example: 1703980800000,
    required: false,
  })
  @IsOptional()
  @IsInt({ message: 'Deadline phải là số nguyên (timestamp)' })
  deadline?: number;
}
