import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho thông tin công việc trong thống kê deadline
 */
export class DeadlineTodoItemDto {
  /**
   * ID của công việc
   */
  @ApiProperty({
    description: 'ID của công việc',
    example: 1,
  })
  id: number;

  /**
   * Tiêu đề công việc
   */
  @ApiProperty({
    description: 'Tiêu đề công việc',
    example: 'Thiết kế giao diện người dùng',
  })
  title: string;

  /**
   * Mô tả công việc
   */
  @ApiProperty({
    description: 'Mô tả công việc',
    example: 'Thiết kế giao diện cho trang chủ',
    nullable: true,
  })
  description: string | null;

  /**
   * ID người được giao
   */
  @ApiProperty({
    description: 'ID người được giao',
    example: 2,
    nullable: true,
  })
  assigneeId: number | null;

  /**
   * Tên người được giao
   */
  @ApiProperty({
    description: 'Tên người được giao',
    example: '<PERSON><PERSON><PERSON><PERSON>n A',
    nullable: true,
  })
  assigneeName: string | null;

  /**
   * ID dự án
   */
  @ApiProperty({
    description: 'ID dự án',
    example: 1,
    nullable: true,
  })
  projectId: number | null;

  /**
   * Tên dự án
   */
  @ApiProperty({
    description: 'Tên dự án',
    example: 'Dự án Website',
    nullable: true,
  })
  projectName: string | null;

  /**
   * Thời gian deadline
   */
  @ApiProperty({
    description: 'Thời gian deadline (timestamp)',
    example: 1703980800000,
    nullable: true,
  })
  deadline: number | null;

  /**
   * Số ngày còn lại đến deadline (âm nếu đã quá hạn)
   */
  @ApiProperty({
    description: 'Số ngày còn lại đến deadline (âm nếu đã quá hạn)',
    example: -2,
  })
  daysRemaining: number;

  /**
   * Trạng thái công việc
   */
  @ApiProperty({
    description: 'Trạng thái công việc',
    example: 'in_progress',
  })
  status: string;

  /**
   * Mức độ ưu tiên
   */
  @ApiProperty({
    description: 'Mức độ ưu tiên',
    example: 'high',
    nullable: true,
  })
  priority: string | null;

  /**
   * Danh sách tags
   */
  @ApiProperty({
    description: 'Danh sách tags',
    example: ['urgent', 'important'],
    nullable: true,
  })
  tags: string[] | null;
}

/**
 * DTO cho response thống kê deadline
 */
export class DeadlineStatisticsResponseDto {
  /**
   * Tổng số công việc có deadline
   */
  @ApiProperty({
    description: 'Tổng số công việc có deadline',
    example: 25,
  })
  totalWithDeadline: number;

  /**
   * Số công việc đã quá hạn
   */
  @ApiProperty({
    description: 'Số công việc đã quá hạn',
    example: 5,
  })
  overdue: number;

  /**
   * Số công việc sắp đến hạn (trong vòng warningDays)
   */
  @ApiProperty({
    description: 'Số công việc sắp đến hạn',
    example: 8,
  })
  nearDeadline: number;

  /**
   * Số công việc còn thời gian
   */
  @ApiProperty({
    description: 'Số công việc còn thời gian',
    example: 12,
  })
  onTime: number;

  /**
   * Danh sách công việc đã quá hạn
   */
  @ApiProperty({
    description: 'Danh sách công việc đã quá hạn',
    type: [DeadlineTodoItemDto],
  })
  overdueItems: DeadlineTodoItemDto[];

  /**
   * Danh sách công việc sắp đến hạn
   */
  @ApiProperty({
    description: 'Danh sách công việc sắp đến hạn',
    type: [DeadlineTodoItemDto],
  })
  nearDeadlineItems: DeadlineTodoItemDto[];

  /**
   * Thống kê theo dự án
   */
  @ApiProperty({
    description: 'Thống kê theo dự án',
    example: {
      'Dự án A': { overdue: 2, nearDeadline: 3, onTime: 5 },
      'Dự án B': { overdue: 1, nearDeadline: 2, onTime: 4 },
    },
  })
  byProject: Record<string, { overdue: number; nearDeadline: number; onTime: number }>;

  /**
   * Thống kê theo người được giao
   */
  @ApiProperty({
    description: 'Thống kê theo người được giao',
    example: {
      'Nguyễn Văn A': { overdue: 1, nearDeadline: 2, onTime: 3 },
      'Trần Thị B': { overdue: 2, nearDeadline: 1, onTime: 4 },
    },
  })
  byAssignee: Record<string, { overdue: number; nearDeadline: number; onTime: number }>;
}
